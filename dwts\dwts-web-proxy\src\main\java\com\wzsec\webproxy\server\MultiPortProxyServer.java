package com.wzsec.webproxy.server;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.service.WebProxyConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 多端口代理服务器管理器
 * 为每个代理配置创建独立的端口监听
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class MultiPortProxyServer {

    @Autowired
    private WebProxyConfigService configService;

    @Autowired
    private ApplicationContext applicationContext;

    // 存储每个端口对应的服务器实例
    private final Map<Integer, ProxyPortServer> portServers = new ConcurrentHashMap<>();

    @PostConstruct
    public void initProxyServers() {
        log.info("多端口代理服务器已禁用，使用Tomcat连接器方式");
        // 禁用独立服务器创建，避免与Tomcat连接器冲突
        // 端口监听由MultiPortConfig中的Tomcat连接器处理
    }

    /**
     * 启动指定配置的代理服务器
     */
    public void startProxyServer(WebProxyConfig config) {
        if (config == null || !config.isValid()) {
            log.warn("代理配置无效，跳过启动: {}", config);
            return;
        }

        Integer port = config.getProxyPort();
        
        // 检查端口是否已经在使用
        if (portServers.containsKey(port)) {
            log.warn("端口{}已经在使用，跳过启动", port);
            return;
        }

        try {
            // 创建代理端口服务器
            ProxyPortServer portServer = new ProxyPortServer(config, applicationContext);
            portServer.start();
            
            portServers.put(port, portServer);
            
            log.info("代理服务器启动成功: {} -> {}:{} (端口:{})", 
                    config.getProxyName(),
                    config.getTargetHost(), 
                    config.getTargetPort(),
                    port);
                    
        } catch (Exception e) {
            log.error("启动代理服务器失败: {} (端口:{})", config.getProxyName(), port, e);
        }
    }

    /**
     * 停止指定端口的代理服务器
     */
    public void stopProxyServer(Integer port) {
        ProxyPortServer portServer = portServers.get(port);
        if (portServer != null) {
            try {
                portServer.stop();
                portServers.remove(port);
                log.info("代理服务器已停止: 端口{}", port);
            } catch (Exception e) {
                log.error("停止代理服务器失败: 端口{}", port, e);
            }
        }
    }

    /**
     * 重启指定配置的代理服务器
     */
    public void restartProxyServer(WebProxyConfig config) {
        Integer port = config.getProxyPort();
        stopProxyServer(port);
        startProxyServer(config);
    }

    /**
     * 获取所有运行中的代理服务器状态
     */
    public Map<Integer, String> getServerStatus() {
        Map<Integer, String> status = new ConcurrentHashMap<>();
        
        for (Map.Entry<Integer, ProxyPortServer> entry : portServers.entrySet()) {
            Integer port = entry.getKey();
            ProxyPortServer server = entry.getValue();
            
            status.put(port, server.isRunning() ? "RUNNING" : "STOPPED");
        }
        
        return status;
    }

    /**
     * 刷新所有代理服务器
     */
    public void refreshAllServers() {
        log.info("刷新所有代理服务器...");
        
        // 停止所有现有服务器
        for (Integer port : portServers.keySet()) {
            stopProxyServer(port);
        }
        
        // 重新初始化
        initProxyServers();
    }

    @PreDestroy
    public void shutdown() {
        log.info("关闭所有代理服务器...");
        
        for (Integer port : portServers.keySet()) {
            stopProxyServer(port);
        }
        
        log.info("所有代理服务器已关闭");
    }

    /**
     * 获取运行中的服务器数量
     */
    public int getRunningServerCount() {
        return (int) portServers.values().stream()
                .filter(ProxyPortServer::isRunning)
                .count();
    }

    /**
     * 检查指定端口是否正在运行
     */
    public boolean isPortRunning(Integer port) {
        ProxyPortServer server = portServers.get(port);
        return server != null && server.isRunning();
    }
}
