package com.wzsec.webproxy.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;

/**
 * Security配置 - 禁用Spring Security
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Configuration
@EnableWebSecurity
@ConditionalOnClass(WebSecurityConfigurerAdapter.class)
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    /**
     * 配置HTTP Security - 允许所有请求
     */
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
            // 禁用CSRF保护
            .csrf().disable()
            // 禁用CORS
            .cors().disable()
            // 禁用X-Frame-Options
            .headers().frameOptions().disable()
            .and()
            // 允许所有请求
            .authorizeRequests()
            .anyRequest().permitAll()
            .and()
            // 禁用HTTP Basic认证
            .httpBasic().disable()
            // 禁用表单登录
            .formLogin().disable()
            // 禁用登出
            .logout().disable();
    }
}
