<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水印测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .test-section h2 {
            color: #555;
            margin-top: 0;
        }
        .test-button {
            display: inline-block;
            padding: 15px 30px;
            margin: 10px;
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
            cursor: pointer;
            border: none;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,123,255,0.4);
        }
        .info-box {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .success-box {
            background: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .warning-box {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .result-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .watermark-demo {
            position: relative;
            height: 200px;
            background: #f0f0f0;
            border: 2px dashed #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>💧 水印功能测试页面</h1>
        
        <div class="info-box">
            <strong>📋 测试说明：</strong>
            <ul>
                <li>这个页面用于测试DWTS代理服务的水印功能</li>
                <li>如果水印正常工作，您应该能看到半透明的水印覆盖在页面上</li>
                <li>水印包含IP地址、时间戳等信息</li>
                <li>水印具有防删除和防修改保护</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🔍 水印检测</h2>
            <div class="watermark-demo">
                <span>水印应该覆盖在这个区域上</span>
            </div>
            <button class="test-button" onclick="checkWatermark()">检测页面水印</button>
            <button class="test-button" onclick="testWatermarkProtection()">测试水印保护</button>
            <div id="watermarkResult" class="result-area" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>🌐 API水印测试</h2>
            <p>测试API接口的水印功能：</p>
            <button class="test-button" onclick="testJsonApi()">测试JSON API</button>
            <button class="test-button" onclick="testXmlApi()">测试XML API</button>
            <div id="apiResult" class="result-area" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>🔗 代理链接测试</h2>
            <p>测试不同代理方式的水印效果：</p>
            <a href="http://localhost:8081/mock/page" class="test-button" target="_blank">本地测试代理</a>
            <a href="http://localhost:8080/" class="test-button" target="_blank">百度代理</a>
            <a href="/?proxy=本地测试服务器" class="test-button" target="_blank">参数代理</a>
        </div>

        <div class="test-section">
            <h2>📊 水印信息</h2>
            <div class="info-box">
                <strong>当前页面信息：</strong><br>
                <strong>URL：</strong><span id="currentUrl"></span><br>
                <strong>时间：</strong><span id="currentTime"></span><br>
                <strong>User-Agent：</strong><span id="userAgent"></span><br>
                <strong>水印状态：</strong><span id="watermarkStatus">检测中...</span>
            </div>
        </div>

        <div class="warning-box">
            <strong>⚠️ 注意事项：</strong>
            <ul>
                <li>水印可能需要几秒钟才能完全加载</li>
                <li>如果看不到水印，请检查浏览器控制台是否有错误</li>
                <li>某些浏览器扩展可能会影响水印显示</li>
                <li>水印透明度可以在代理配置中调整</li>
            </ul>
        </div>
    </div>

    <script>
        // 页面加载时初始化
        window.onload = function() {
            document.getElementById('currentUrl').textContent = window.location.href;
            document.getElementById('currentTime').textContent = new Date().toLocaleString();
            document.getElementById('userAgent').textContent = navigator.userAgent;
            
            // 延迟检测水印
            setTimeout(checkWatermark, 2000);
        };

        // 检测页面水印
        function checkWatermark() {
            const resultDiv = document.getElementById('watermarkResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在检测水印...';
            
            let watermarkFound = false;
            let watermarkInfo = [];
            
            // 检查水印样式
            const watermarkStyle = document.getElementById('dwts-watermark-style');
            if (watermarkStyle) {
                watermarkFound = true;
                watermarkInfo.push('✓ 发现水印样式');
            }
            
            // 检查水印元素
            const watermarkDiv = document.getElementById('dwts-watermark-div');
            if (watermarkDiv) {
                watermarkFound = true;
                watermarkInfo.push('✓ 发现水印元素');
            }
            
            // 检查水印脚本
            const watermarkScript = document.getElementById('dwts-watermark-script');
            if (watermarkScript) {
                watermarkFound = true;
                watermarkInfo.push('✓ 发现水印脚本');
            }
            
            // 检查CSS类
            const watermarkElements = document.getElementsByClassName('dwts-watermark');
            if (watermarkElements.length > 0) {
                watermarkFound = true;
                watermarkInfo.push(`✓ 发现${watermarkElements.length}个水印元素`);
            }
            
            // 检查HTTP头
            fetch(window.location.href)
                .then(response => {
                    const watermarkHeader = response.headers.get('X-DWTS-Watermark');
                    if (watermarkHeader) {
                        watermarkInfo.push(`✓ 发现水印头: ${watermarkHeader}`);
                    }
                    
                    const watermarkTime = response.headers.get('X-DWTS-Watermark-Time');
                    if (watermarkTime) {
                        watermarkInfo.push(`✓ 水印时间: ${new Date(parseInt(watermarkTime)).toLocaleString()}`);
                    }
                    
                    updateWatermarkResult(watermarkFound, watermarkInfo);
                })
                .catch(error => {
                    updateWatermarkResult(watermarkFound, watermarkInfo);
                });
        }
        
        function updateWatermarkResult(found, info) {
            const resultDiv = document.getElementById('watermarkResult');
            const statusSpan = document.getElementById('watermarkStatus');
            
            if (found) {
                resultDiv.textContent = '水印检测结果：\n' + info.join('\n');
                statusSpan.textContent = '✓ 已启用';
                statusSpan.style.color = 'green';
            } else {
                resultDiv.textContent = '❌ 未检测到水印\n请检查代理配置是否正确';
                statusSpan.textContent = '✗ 未检测到';
                statusSpan.style.color = 'red';
            }
        }

        // 测试水印保护
        function testWatermarkProtection() {
            const resultDiv = document.getElementById('watermarkResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '测试水印保护功能...';
            
            const watermarkDiv = document.getElementById('dwts-watermark-div');
            if (watermarkDiv) {
                try {
                    // 尝试删除水印
                    watermarkDiv.remove();
                    
                    // 检查是否自动恢复
                    setTimeout(() => {
                        const newWatermark = document.getElementById('dwts-watermark-div');
                        if (newWatermark) {
                            resultDiv.textContent = '✓ 水印保护功能正常\n水印被删除后自动恢复';
                        } else {
                            resultDiv.textContent = '⚠️ 水印保护功能可能存在问题\n水印被删除后未自动恢复';
                        }
                    }, 1000);
                } catch (error) {
                    resultDiv.textContent = '✓ 水印保护功能正常\n无法删除水印元素: ' + error.message;
                }
            } else {
                resultDiv.textContent = '❌ 未找到水印元素，无法测试保护功能';
            }
        }

        // 测试JSON API
        function testJsonApi() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在测试JSON API...';
            
            fetch('/mock/api/json')
                .then(response => response.json())
                .then(data => {
                    let result = 'JSON API测试结果：\n';
                    result += JSON.stringify(data, null, 2);
                    
                    // 检查是否包含水印字段
                    if (data.watermark || data._watermark || data.dwts_watermark) {
                        result += '\n\n✓ 检测到API水印字段';
                    } else {
                        result += '\n\n⚠️ 未检测到API水印字段';
                    }
                    
                    resultDiv.textContent = result;
                })
                .catch(error => {
                    resultDiv.textContent = '❌ JSON API测试失败: ' + error.message;
                });
        }

        // 测试XML API
        function testXmlApi() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在测试XML API...';
            
            fetch('/mock/api/xml')
                .then(response => response.text())
                .then(data => {
                    let result = 'XML API测试结果：\n';
                    result += data;
                    
                    // 检查是否包含水印节点
                    if (data.includes('<watermark>') || data.includes('<dwts_watermark>')) {
                        result += '\n\n✓ 检测到API水印节点';
                    } else {
                        result += '\n\n⚠️ 未检测到API水印节点';
                    }
                    
                    resultDiv.textContent = result;
                })
                .catch(error => {
                    resultDiv.textContent = '❌ XML API测试失败: ' + error.message;
                });
        }
    </script>
</body>
</html>
