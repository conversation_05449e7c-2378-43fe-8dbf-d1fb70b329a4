package com.wzsec.webproxy.controller;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.service.WebProxyConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Web代理配置管理控制器
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@RestController
@RequestMapping("/api/proxy/config")
public class WebProxyConfigController {

    @Autowired
    private WebProxyConfigService configService;

    /**
     * 获取所有代理配置
     */
    @GetMapping("/list")
    public ResponseEntity<List<WebProxyConfig>> getAllConfigs() {
        List<WebProxyConfig> configs = configService.getAllActiveConfigs();
        return ResponseEntity.ok(configs);
    }

    /**
     * 根据ID获取代理配置
     */
    @GetMapping("/{id}")
    public ResponseEntity<WebProxyConfig> getConfigById(@PathVariable Long id) {
        // 这里需要添加根据ID查询的方法
        return ResponseEntity.notFound().build();
    }

    /**
     * 根据端口获取代理配置
     */
    @GetMapping("/port/{port}")
    public ResponseEntity<WebProxyConfig> getConfigByPort(@PathVariable Integer port) {
        WebProxyConfig config = configService.getConfigByPort(port);
        if (config != null) {
            return ResponseEntity.ok(config);
        }
        return ResponseEntity.notFound().build();
    }

    /**
     * 创建代理配置
     */
    @PostMapping("/create")
    public ResponseEntity<Map<String, Object>> createConfig(@Valid @RequestBody WebProxyConfig config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            WebProxyConfig savedConfig = configService.saveConfig(config);
            result.put("success", true);
            result.put("message", "代理配置创建成功");
            result.put("data", savedConfig);
            
            log.info("创建代理配置成功: {} -> {}:{}", 
                    savedConfig.getProxyPort(), 
                    savedConfig.getTargetHost(), 
                    savedConfig.getTargetPort());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "创建代理配置失败: " + e.getMessage());
            
            log.error("创建代理配置失败", e);
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 更新代理配置
     */
    @PutMapping("/update")
    public ResponseEntity<Map<String, Object>> updateConfig(@Valid @RequestBody WebProxyConfig config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            WebProxyConfig updatedConfig = configService.updateConfig(config);
            result.put("success", true);
            result.put("message", "代理配置更新成功");
            result.put("data", updatedConfig);
            
            log.info("更新代理配置成功: {}", updatedConfig.getProxyName());
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新代理配置失败: " + e.getMessage());
            
            log.error("更新代理配置失败", e);
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 删除代理配置
     */
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<Map<String, Object>> deleteConfig(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            configService.deleteConfig(id);
            result.put("success", true);
            result.put("message", "代理配置删除成功");
            
            log.info("删除代理配置成功: {}", id);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除代理配置失败: " + e.getMessage());
            
            log.error("删除代理配置失败", e);
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 启用代理配置
     */
    @PostMapping("/enable/{id}")
    public ResponseEntity<Map<String, Object>> enableConfig(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            configService.enableConfig(id);
            result.put("success", true);
            result.put("message", "代理配置启用成功");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "启用代理配置失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 禁用代理配置
     */
    @PostMapping("/disable/{id}")
    public ResponseEntity<Map<String, Object>> disableConfig(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            configService.disableConfig(id);
            result.put("success", true);
            result.put("message", "代理配置禁用成功");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "禁用代理配置失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 获取配置统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getConfigStats() {
        Map<String, Object> stats = configService.getConfigStats();
        return ResponseEntity.ok(stats);
    }

    /**
     * 刷新配置缓存
     */
    @PostMapping("/refresh")
    public ResponseEntity<Map<String, Object>> refreshCache() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            configService.refreshCache();
            result.put("success", true);
            result.put("message", "配置缓存刷新成功");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "刷新配置缓存失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 快速创建百度代理配置
     */
    @PostMapping("/create-baidu")
    public ResponseEntity<Map<String, Object>> createBaiduProxy(@RequestParam(defaultValue = "8081") Integer port,
                                                               @RequestParam(defaultValue = "百度代理水印") String watermarkText) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            WebProxyConfig config = new WebProxyConfig();
            config.setProxyName("百度代理");
            config.setProxyPort(port);
            config.setTargetHost("www.baidu.com");
            config.setTargetPort(443);
            config.setTargetProtocol("https");
            config.setWatermarkText(watermarkText);
            config.setEnablePageWatermark(true);
            config.setEnableApiWatermark(true);
            config.setWatermarkOpacity(0.15);
            config.setWatermarkColor("#FF0000");
            config.setWatermarkAngle(-30.0);
            config.setRemark("百度网站代理，自动添加水印");
            config.setCreateUser("system");
            
            WebProxyConfig savedConfig = configService.saveConfig(config);
            result.put("success", true);
            result.put("message", "百度代理配置创建成功");
            result.put("data", savedConfig);
            result.put("accessUrl", "http://localhost:" + port);
            
            log.info("创建百度代理配置成功，访问地址: http://localhost:{}", port);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "创建百度代理配置失败: " + e.getMessage());
            
            log.error("创建百度代理配置失败", e);
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 快速创建测试代理配置
     */
    @PostMapping("/create-test")
    public ResponseEntity<Map<String, Object>> createTestProxy(@RequestParam(defaultValue = "8081") Integer port) {
        Map<String, Object> result = new HashMap<>();

        try {
            WebProxyConfig config = new WebProxyConfig();
            config.setProxyName("本地测试服务器");
            config.setProxyPort(port);
            config.setTargetHost("localhost");
            config.setTargetPort(9090);
            config.setTargetProtocol("http");
            config.setWatermarkText("测试水印_{IP}_{TIME}");
            config.setEnablePageWatermark(true);
            config.setEnableApiWatermark(true);
            config.setApiPathPatterns("/api/**,/mock/api/**");
            config.setWatermarkOpacity(0.2);
            config.setWatermarkColor("#FF0000");
            config.setWatermarkAngle(-30.0);
            config.setRemark("用于测试代理和水印功能的本地服务器");
            config.setCreateUser("system");

            WebProxyConfig savedConfig = configService.saveConfig(config);
            result.put("success", true);
            result.put("message", "测试代理配置创建成功");
            result.put("data", savedConfig);
            result.put("accessUrl", "http://localhost:" + port + "/mock/page");
            result.put("testUrls", new String[]{
                "http://localhost:" + port + "/mock/page",
                "http://localhost:" + port + "/mock/api/json",
                "http://localhost:" + port + "/mock/api/xml"
            });

            log.info("创建测试代理配置成功，访问地址: http://localhost:{}/mock/page", port);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "创建测试代理配置失败: " + e.getMessage());

            log.error("创建测试代理配置失败", e);
            return ResponseEntity.badRequest().body(result);
        }
    }
}
