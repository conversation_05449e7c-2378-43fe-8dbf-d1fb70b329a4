package com.wzsec.webproxy.config;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.service.WebProxyConfigService;
import com.wzsec.webproxy.util.PortChecker;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.Connector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 多端口配置
 * 为每个代理配置创建额外的Tomcat连接器
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Configuration
public class MultiPortConfig {

    @Autowired
    private WebProxyConfigService configService;

    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> containerCustomizer() {
        return factory -> {
            try {
                // 检查端口状态
                log.info("检查代理端口状态...");
                PortChecker.checkPorts(8080, 8081, 8082);

                // 硬编码添加已知的代理端口，避免数据库依赖问题
                int[] proxyPorts = {8080, 8081, 8082};
                int addedPorts = 0;

                for (int port : proxyPorts) {
                    if (port != 9090) { // 避免与主应用端口冲突
                        if (PortChecker.isPortAvailable(port)) {
                            // 为每个代理端口创建额外的连接器
                            Connector connector = new Connector("org.apache.coyote.http11.Http11NioProtocol");
                            connector.setPort(port);
                            connector.setScheme("http");
                            connector.setSecure(false);

                            factory.addAdditionalTomcatConnectors(connector);
                            addedPorts++;

                            log.info("添加代理端口连接器: 端口{}", port);
                        } else {
                            log.warn("端口{}被占用，跳过添加连接器", port);
                            log.info("端口{}占用详情:\n{}", port, PortChecker.getPortProcess(port));
                        }
                    }
                }

                log.info("多端口配置完成，成功配置{}个代理端口", addedPorts);

            } catch (Exception e) {
                log.error("配置多端口失败", e);
            }
        };
    }
}
