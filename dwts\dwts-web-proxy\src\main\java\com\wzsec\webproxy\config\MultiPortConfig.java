package com.wzsec.webproxy.config;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.service.WebProxyConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.Connector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 多端口配置
 * 为每个代理配置创建额外的Tomcat连接器
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Configuration
public class MultiPortConfig {

    @Autowired
    private WebProxyConfigService configService;

    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> containerCustomizer() {
        return factory -> {
            try {
                // 延迟获取配置，避免循环依赖
                if (configService != null) {
                    List<WebProxyConfig> configs = configService.getAllActiveConfigs();

                    for (WebProxyConfig config : configs) {
                        if (config.getProxyPort() != 9090) { // 避免与主应用端口冲突
                            // 为每个代理配置创建额外的连接器
                            Connector connector = new Connector("org.apache.coyote.http11.Http11NioProtocol");
                            connector.setPort(config.getProxyPort());
                            connector.setScheme("http");
                            connector.setSecure(false);

                            factory.addAdditionalTomcatConnectors(connector);

                            log.info("添加代理端口连接器: {} -> {}:{} (端口:{})",
                                    config.getProxyName(),
                                    config.getTargetHost(),
                                    config.getTargetPort(),
                                    config.getProxyPort());
                        }
                    }

                    log.info("多端口配置完成，共配置{}个代理端口", configs.size());
                } else {
                    log.warn("配置服务未就绪，跳过多端口配置");
                }

            } catch (Exception e) {
                log.error("配置多端口失败", e);
            }
        };
    }
}
