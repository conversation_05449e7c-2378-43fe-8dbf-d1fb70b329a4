package com.wzsec.webproxy.config;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.service.WebProxyConfigService;
import com.wzsec.webproxy.util.PortChecker;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.Connector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 多端口配置
 * 为每个代理配置创建额外的Tomcat连接器
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Configuration
public class MultiPortConfig {

    @Autowired
    private WebProxyConfigService configService;

    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> containerCustomizer() {
        return factory -> {
            // 暂时禁用多端口配置，避免冲突
            log.info("多端口配置已禁用，所有请求通过主端口9090处理");

            // TODO: 后续可以通过应用启动完成后动态添加端口的方式实现
        };
    }
}
