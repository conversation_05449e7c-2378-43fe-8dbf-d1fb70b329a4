<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DWTS Web代理服务</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h2 {
            color: #666;
            margin-top: 0;
        }
        .proxy-link {
            display: inline-block;
            padding: 10px 20px;
            margin: 10px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        .proxy-link:hover {
            background-color: #0056b3;
        }
        .info {
            background-color: #e7f3ff;
            padding: 15px;
            border-left: 4px solid #007bff;
            margin: 15px 0;
        }
        .warning {
            background-color: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 DWTS Web代理服务</h1>
        
        <div class="info">
            <strong>服务状态：</strong>运行中 | <strong>端口：</strong>9090 | <strong>功能：</strong>网页代理 + 自动水印
        </div>

        <div class="section">
            <h2>🚀 快速开始</h2>
            <p>点击下面的链接直接访问代理网站：</p>
            <a href="http://localhost:8080/" class="proxy-link" target="_blank">📱 百度代理（端口8080）</a>
            <a href="http://localhost:8081/mock/page" class="proxy-link" target="_blank">🧪 测试代理（端口8081）</a>
            <a href="http://localhost:8082/" class="proxy-link" target="_blank">🌐 内网服务（端口8082）</a>
            <a href="/mock/page" class="proxy-link" target="_blank">🔧 直接测试页面</a>
            <a href="/?proxy=百度代理" class="proxy-link" target="_blank">🔗 参数代理（百度）</a>
            <a href="/test/health" class="proxy-link" target="_blank">❤️ 健康检查</a>
        </div>

        <div class="section">
            <h2>📋 代理配置</h2>
            <table>
                <thead>
                    <tr>
                        <th>访问方式</th>
                        <th>URL</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>百度代理</td>
                        <td><code>http://localhost:8080/</code></td>
                        <td>直接访问百度，自动添加水印</td>
                    </tr>
                    <tr>
                        <td>本地服务代理</td>
                        <td><code>http://localhost:8081/</code></td>
                        <td>代理到 127.0.0.1:8013</td>
                    </tr>
                    <tr>
                        <td>内网服务代理</td>
                        <td><code>http://localhost:8082/</code></td>
                        <td>代理到 *************:3000</td>
                    </tr>
                    <tr>
                        <td>管理接口</td>
                        <td><code>http://localhost:9090/api/proxy/config/list</code></td>
                        <td>查看所有代理配置</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>⚙️ API接口</h2>
            <div class="code">
                GET /test/health - 健康检查<br>
                GET /test/proxy-status - 代理状态<br>
                GET /test/usage - 使用说明<br>
                GET /api/proxy/config/list - 获取代理配置<br>
                POST /api/proxy/config/create-baidu?port=8083 - 创建百度代理
            </div>
        </div>

        <div class="section">
            <h2>💡 水印功能</h2>
            <div class="info">
                <strong>自动水印：</strong>系统会自动为HTML页面添加水印，包括：
                <ul>
                    <li>页面水印：在网页上添加半透明文字水印</li>
                    <li>API水印：为JSON/XML响应添加水印字段</li>
                    <li>Header水印：在HTTP响应头中添加水印信息</li>
                </ul>
            </div>
        </div>

        <div class="warning">
            <strong>注意事项：</strong>
            <ul>
                <li>确保目标网站可以正常访问</li>
                <li>某些网站可能有防代理机制</li>
                <li>HTTPS网站需要正确的SSL配置</li>
                <li>水印功能可能影响页面加载速度</li>
            </ul>
        </div>

        <div class="section">
            <h2>🔧 故障排除</h2>
            <p>如果代理无法正常工作，请检查：</p>
            <ol>
                <li>应用是否正常启动在9090端口</li>
                <li>代理配置是否正确加载</li>
                <li>目标网站是否可以直接访问</li>
                <li>查看应用日志获取详细错误信息</li>
            </ol>
        </div>
    </div>

    <script>
        // 页面加载完成后检查服务状态
        window.onload = function() {
            fetch('/test/health')
                .then(response => response.json())
                .then(data => {
                    console.log('服务状态:', data);
                })
                .catch(error => {
                    console.error('服务检查失败:', error);
                });
        };
    </script>
</body>
</html>
