-- 更新代理配置用于测试
-- 将8081端口的配置改为指向本地模拟服务器

USE `dwts-clean`;

-- 更新8081端口配置为本地测试服务器
UPDATE `dwts_web_proxy_config` 
SET 
    `proxy_name` = '本地测试服务器',
    `target_host` = 'localhost',
    `target_port` = 9090,
    `target_protocol` = 'http',
    `watermark_text` = '测试水印_{IP}_{TIME}',
    `enable_page_watermark` = 1,
    `enable_api_watermark` = 1,
    `watermark_opacity` = 0.2,
    `watermark_color` = '#FF0000',
    `watermark_angle` = -30.0,
    `remark` = '用于测试代理和水印功能的本地服务器',
    `update_time` = NOW()
WHERE `proxy_port` = 8081;

-- 如果不存在8081配置，则插入一个
INSERT IGNORE INTO `dwts_web_proxy_config` (
    `proxy_name`, `proxy_port`, `target_host`, `target_port`, `target_protocol`,
    `watermark_text`, `enable_page_watermark`, `enable_api_watermark`,
    `api_path_patterns`, `watermark_opacity`, `watermark_width`, `watermark_height`,
    `watermark_color`, `watermark_angle`, `enable_link_rewrite`, `enable_api_intercept`,
    `status`, `remark`, `create_user`
) VALUES (
    '本地测试服务器', 8081, 'localhost', 9090, 'http',
    '测试水印_{IP}_{TIME}', 1, 1,
    '/api/**,/mock/api/**', 0.2, 300, 150,
    '#FF0000', -30.0, 1, 1,
    'ACTIVE', '用于测试代理和水印功能的本地服务器', 'system'
);

-- 确保百度代理配置存在
INSERT IGNORE INTO `dwts_web_proxy_config` (
    `proxy_name`, `proxy_port`, `target_host`, `target_port`, `target_protocol`,
    `watermark_text`, `enable_page_watermark`, `enable_api_watermark`,
    `api_path_patterns`, `watermark_opacity`, `watermark_width`, `watermark_height`,
    `watermark_color`, `watermark_angle`, `enable_link_rewrite`, `enable_api_intercept`,
    `status`, `remark`, `create_user`
) VALUES (
    '百度代理', 8080, 'www.baidu.com', 443, 'https',
    '百度代理水印_{IP}_{TIME}', 1, 1,
    '/api/**,/rest/**', 0.15, 300, 150,
    '#0066CC', -30.0, 1, 1,
    'ACTIVE', '百度网站代理，自动添加水印', 'system'
);

-- 查看更新结果
SELECT 
    proxy_name,
    proxy_port,
    target_host,
    target_port,
    target_protocol,
    watermark_text,
    enable_page_watermark,
    enable_api_watermark,
    status
FROM `dwts_web_proxy_config` 
WHERE `status` = 'ACTIVE'
ORDER BY `proxy_port`;
