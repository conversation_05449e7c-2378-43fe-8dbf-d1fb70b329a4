package com.wzsec.webproxy.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate配置
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Configuration
public class RestTemplateConfig {

    @Value("${web-proxy.rest-template.connect-timeout:10000}")
    private int connectTimeout;

    @Value("${web-proxy.rest-template.read-timeout:60000}")
    private int readTimeout;

    @Value("${web-proxy.rest-template.max-connections:500}")
    private int maxConnections;

    @Value("${web-proxy.rest-template.max-connections-per-route:50}")
    private int maxConnectionsPerRoute;

    @Bean
    public RestTemplate restTemplate() {
        // 创建连接池管理器
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(maxConnections);
        connectionManager.setDefaultMaxPerRoute(maxConnectionsPerRoute);

        // 创建请求配置
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(connectTimeout)
                .setSocketTimeout(readTimeout)
                .setConnectionRequestTimeout(connectTimeout)
                .build();

        // 创建HttpClient
        HttpClient httpClient = HttpClientBuilder.create()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig)
                .build();

        // 创建请求工厂
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(httpClient);

        // 创建RestTemplate
        RestTemplate restTemplate = new RestTemplate(factory);

        log.info("RestTemplate配置完成 - 连接超时: {}ms, 读取超时: {}ms, 最大连接数: {}, 每路由最大连接数: {}",
                connectTimeout, readTimeout, maxConnections, maxConnectionsPerRoute);

        return restTemplate;
    }
}
