package com.wzsec.webproxy.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;

/**
 * 百度代理特殊处理器
 * 处理百度网站的反代理机制
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class BaiduProxyHandler {

    /**
     * 为百度请求创建特殊的请求头
     */
    public HttpHeaders createBaiduHeaders(HttpServletRequest request) {
        HttpHeaders headers = new HttpHeaders();
        
        // 复制原始请求头，但跳过一些特殊头部
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            
            if (shouldSkipHeader(headerName)) {
                continue;
            }
            
            Enumeration<String> headerValues = request.getHeaders(headerName);
            while (headerValues.hasMoreElements()) {
                headers.add(headerName, headerValues.nextElement());
            }
        }
        
        // 设置百度特定的请求头
        headers.set("Host", "www.baidu.com");
        headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        headers.set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
        headers.set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
        headers.set("Accept-Encoding", "gzip, deflate, br");
        headers.set("DNT", "1");
        headers.set("Connection", "keep-alive");
        headers.set("Upgrade-Insecure-Requests", "1");
        headers.set("Sec-Fetch-Dest", "document");
        headers.set("Sec-Fetch-Mode", "navigate");
        headers.set("Sec-Fetch-Site", "none");
        headers.set("Sec-Fetch-User", "?1");
        headers.set("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"");
        headers.set("sec-ch-ua-mobile", "?0");
        headers.set("sec-ch-ua-platform", "\"Windows\"");
        
        // 移除可能导致问题的头部
        headers.remove("X-Forwarded-For");
        headers.remove("X-Forwarded-Proto");
        headers.remove("X-Forwarded-Host");
        headers.remove("X-Real-IP");
        
        return headers;
    }

    /**
     * 处理百度响应内容
     */
    public String processBaiduContent(String content, HttpServletRequest request) {
        if (content == null || content.trim().isEmpty()) {
            log.warn("百度响应内容为空");
            return content;
        }
        
        // 替换百度内部链接为代理链接
        String proxyHost = "localhost:" + request.getLocalPort();
        
        // 替换绝对链接
        content = content.replaceAll("https://www\\.baidu\\.com", "http://" + proxyHost);
        content = content.replaceAll("http://www\\.baidu\\.com", "http://" + proxyHost);
        content = content.replaceAll("//www\\.baidu\\.com", "//" + proxyHost);
        
        // 替换相对链接
        content = content.replaceAll("href=\"/", "href=\"http://" + proxyHost + "/");
        content = content.replaceAll("src=\"/", "src=\"http://" + proxyHost + "/");
        content = content.replaceAll("action=\"/", "action=\"http://" + proxyHost + "/");
        
        // 处理JavaScript中的链接
        content = content.replaceAll("location\\.href\\s*=\\s*['\"]([^'\"]*)['\"]", 
                "location.href = 'http://" + proxyHost + "$1'");
        
        // 处理CSS中的链接
        content = content.replaceAll("url\\(['\"]?/([^'\"\\)]*)['\"]?\\)", 
                "url('http://" + proxyHost + "/$1')");
        
        log.debug("百度内容处理完成，内容长度: {}", content.length());
        
        return content;
    }

    /**
     * 检查是否应该跳过某个请求头
     */
    private boolean shouldSkipHeader(String headerName) {
        String lowerName = headerName.toLowerCase();
        return lowerName.equals("host") || 
               lowerName.equals("content-length") ||
               lowerName.startsWith("x-forwarded-") ||
               lowerName.equals("connection") ||
               lowerName.equals("upgrade") ||
               lowerName.equals("proxy-connection") ||
               lowerName.equals("transfer-encoding") ||
               lowerName.equals("te") ||
               lowerName.startsWith("sec-") ||
               lowerName.equals("origin") ||
               lowerName.equals("referer");
    }

    /**
     * 检查是否是百度相关的请求
     */
    public boolean isBaiduRequest(String targetHost) {
        return targetHost != null && 
               (targetHost.contains("baidu.com") || 
                targetHost.contains("百度"));
    }

    /**
     * 获取百度首页的默认内容（当请求失败时使用）
     */
    public String getBaiduFallbackContent(HttpServletRequest request) {
        String proxyHost = "localhost:" + request.getLocalPort();
        
        return "<!DOCTYPE html>\n" +
                "<html lang=\"zh-CN\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                "    <title>百度一下，你就知道</title>\n" +
                "    <style>\n" +
                "        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }\n" +
                "        .logo { font-size: 48px; color: #3385ff; margin-bottom: 30px; }\n" +
                "        .search-box { margin: 20px 0; }\n" +
                "        .search-input { width: 400px; height: 40px; font-size: 16px; padding: 0 10px; border: 2px solid #c4c7ce; }\n" +
                "        .search-btn { height: 44px; width: 100px; background: #3385ff; color: white; border: none; font-size: 16px; cursor: pointer; }\n" +
                "        .notice { color: #999; margin-top: 30px; }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class=\"logo\">百度</div>\n" +
                "    <div class=\"search-box\">\n" +
                "        <form action=\"http://" + proxyHost + "/s\" method=\"get\">\n" +
                "            <input type=\"text\" name=\"wd\" class=\"search-input\" placeholder=\"请输入搜索内容\">\n" +
                "            <input type=\"submit\" value=\"百度一下\" class=\"search-btn\">\n" +
                "        </form>\n" +
                "    </div>\n" +
                "    <div class=\"notice\">\n" +
                "        这是通过DWTS代理访问的百度页面<br>\n" +
                "        如果无法正常显示，请检查网络连接或联系管理员\n" +
                "    </div>\n" +
                "</body>\n" +
                "</html>";
    }
}
