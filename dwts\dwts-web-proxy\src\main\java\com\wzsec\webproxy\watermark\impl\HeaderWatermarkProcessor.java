package com.wzsec.webproxy.watermark.impl;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.watermark.AbstractWatermarkProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * HTTP头水印处理器（默认处理器）
 * 用于处理无法直接修改内容的响应类型
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class HeaderWatermarkProcessor extends AbstractWatermarkProcessor {

    @Override
    public byte[] processWatermark(byte[] content, String contentType, 
                                 HttpServletRequest request, WebProxyConfig config) {
        // HTTP头水印不修改响应体内容，只在响应头中添加水印信息
        // 实际的头部添加会在WebProxyService中处理
        return content;
    }

    /**
     * 生成水印头部信息
     * 这个方法会被WebProxyService调用来获取需要添加的头部信息
     */
    public String generateWatermarkHeader(HttpServletRequest request, WebProxyConfig config) {
        return generateWatermarkText(request, config);
    }

    @Override
    public boolean canHandle(String contentType) {
        // 作为默认处理器，可以处理任何类型
        return true;
    }

    @Override
    public String getProcessorName() {
        return "HeaderWatermarkProcessor";
    }

    @Override
    public String getWatermarkType() {
        return "HEADER";
    }
}
