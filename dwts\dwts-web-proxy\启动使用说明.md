# DWTS Web代理服务 - 启动使用说明

## 🚀 快速启动

### 1. 数据库初始化
```bash
# 执行数据库初始化脚本
mysql -u root -p < src/main/resources/sql/init.sql
```

### 2. 修改配置
编辑 `src/main/resources/application.yml`：
```yaml
spring:
  datasource:
    url: **************************************
    username: root
    password: your_password  # 修改为你的MySQL密码
```

### 3. 启动服务
```bash
# 方式一：使用Maven启动
mvn spring-boot:run

# 方式二：打包后启动
mvn clean package
java -jar target/dwts-web-proxy-1.0.0.jar
```

### 4. 验证启动
访问健康检查接口：http://localhost:8080/actuator/health

## 📋 代理配置

数据库初始化后，已包含以下示例配置：

| 代理端口 | 目标地址 | 代理名称 | 状态 |
|---------|----------|----------|------|
| 8080 | *************:80 | Vue应用代理示例 | 激活 |
| 8081 | *************:8080 | API服务代理 | 激活 |
| 8082 | *************:3000 | 管理后台代理 | 激活 |

### 修改代理目标
```sql
-- 修改代理目标地址（根据你的实际情况修改）
UPDATE dwts_web_proxy_config 
SET target_host = '你的目标服务器IP', target_port = 你的目标端口
WHERE proxy_name = 'Vue应用代理示例';

-- 例如：代理本地的Vue应用
UPDATE dwts_web_proxy_config 
SET target_host = '127.0.0.1', target_port = 3000
WHERE proxy_name = 'Vue应用代理示例';
```

## 🌐 使用示例

### 场景一：代理Vue应用

**假设你有一个Vue应用运行在 `http://localhost:3000`**

1. **修改代理配置**：
```sql
UPDATE dwts_web_proxy_config 
SET target_host = '127.0.0.1', target_port = 3000
WHERE proxy_name = 'Vue应用代理示例';
```

2. **访问方式对比**：
```bash
# 原始访问（无水印）
http://localhost:3000/

# 代理访问（带水印）
http://localhost:8080/
```

3. **效果**：
   - 页面会显示半透明的重复水印
   - 页面中的API调用会自动通过代理
   - API响应会包含水印信息

### 场景二：代理Nginx静态网站

**假设你有一个Nginx服务运行在 `http://*************:80`**

1. **使用默认配置**（已配置好）

2. **访问方式**：
```bash
# 原始访问
http://*************/

# 代理访问（带水印）
http://localhost:8080/
```

### 场景三：仅代理API接口

**假设你只想对API接口添加水印，不处理页面**

1. **修改配置**：
```sql
UPDATE dwts_web_proxy_config 
SET enable_page_watermark = 0, enable_api_watermark = 1
WHERE proxy_name = 'Vue应用代理示例';
```

2. **API访问**：
```bash
# 原始API
curl http://localhost:3000/api/users

# 代理API（带水印）
curl http://localhost:8080/api/users
```

## 🎨 水印效果

### 页面水印
- **位置**：覆盖整个页面
- **样式**：半透明重复水印
- **内容**：包含IP地址和时间戳
- **防护**：防止被删除或修改

### API水印
**JSON响应示例**：
```json
{
  "code": 200,
  "data": [
    {"id": 1, "name": "用户1"},
    {"id": 2, "name": "用户2"}
  ],
  "_watermark": {
    "text": "DWTS水印_*************_20241219",
    "timestamp": 1703001234567,
    "source": "DWTS-WEB-PROXY",
    "ip": "*************",
    "user": "ANONYMOUS",
    "path": "/api/users",
    "method": "GET"
  }
}
```

## 🔧 常用配置

### 修改水印文本
```sql
UPDATE dwts_web_proxy_config 
SET watermark_text = '我的水印_{IP}_{DATE}'
WHERE proxy_name = 'Vue应用代理示例';
```

### 调整水印透明度
```sql
UPDATE dwts_web_proxy_config 
SET watermark_opacity = 0.15  -- 0.0-1.0之间
WHERE proxy_name = 'Vue应用代理示例';
```

### 添加新的代理配置
```sql
INSERT INTO dwts_web_proxy_config (
    proxy_name, proxy_port, target_host, target_port,
    watermark_text, status, create_user
) VALUES (
    '我的应用', 8090, '*************', 80,
    '我的水印_{IP}_{TIME}', 'ACTIVE', 'admin'
);
```

## 📊 监控和日志

### 查看访问记录
```sql
-- 查看最近10条访问记录
SELECT request_ip, request_path, request_method, watermark_added, create_time
FROM dwts_web_proxy_record 
ORDER BY create_time DESC 
LIMIT 10;
```

### 查看代理统计
```sql
-- 查看代理配置统计
SELECT * FROM v_proxy_config_stats;

-- 查看指定代理的详细统计
CALL sp_get_proxy_stats(1, 7);  -- 配置ID=1，最近7天
```

### 应用日志
```bash
# 查看应用日志
tail -f logs/dwts-web-proxy.log

# 查看错误日志
tail -f logs/dwts-web-proxy.log | grep ERROR
```

## ⚠️ 注意事项

1. **端口占用**：确保代理端口（8080、8081、8082）没有被其他服务占用
2. **目标服务**：确保目标服务器地址可以正常访问
3. **防火墙**：如果目标服务器有防火墙，需要允许代理服务器的访问
4. **HTTPS**：如果目标服务使用HTTPS，需要修改`target_protocol`为`https`
5. **跨域**：代理会自动处理跨域问题

## 🔍 故障排除

### 代理无响应
1. 检查目标服务器是否可访问：`curl http://目标IP:端口`
2. 检查代理配置是否正确：`SELECT * FROM dwts_web_proxy_config WHERE status='ACTIVE'`
3. 查看应用日志是否有错误信息

### 水印不显示
1. 确认水印功能已启用：`enable_page_watermark=1`
2. 检查浏览器控制台是否有JavaScript错误
3. 确认响应内容类型是否为HTML

### 性能问题
1. 调整数据库连接池大小
2. 定期清理访问记录：`CALL sp_cleanup_old_records(30)`
3. 增加JVM内存：`java -Xmx2g -jar app.jar`

## 📞 技术支持

如有问题，请检查：
1. MySQL服务是否正常运行
2. 数据库连接配置是否正确
3. 目标服务器网络是否可达
4. 应用日志中的错误信息
