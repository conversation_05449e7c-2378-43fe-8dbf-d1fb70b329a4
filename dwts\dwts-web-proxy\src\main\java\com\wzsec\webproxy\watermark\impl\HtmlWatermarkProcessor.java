package com.wzsec.webproxy.watermark.impl;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.watermark.AbstractWatermarkProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.regex.Pattern;

/**
 * HTML页面水印处理器
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class HtmlWatermarkProcessor extends AbstractWatermarkProcessor {

    private static final Pattern HEAD_PATTERN = Pattern.compile("</head>", Pattern.CASE_INSENSITIVE);
    private static final Pattern BODY_PATTERN = Pattern.compile("</body>", Pattern.CASE_INSENSITIVE);

    @Override
    public byte[] processWatermark(byte[] content, String contentType,
                                 HttpServletRequest request, WebProxyConfig config) {
        try {
            log.info("开始处理HTML水印 - ContentType: {}, EnablePageWatermark: {}",
                    contentType, config.getEnablePageWatermark());

            if (!config.getEnablePageWatermark()) {
                log.info("页面水印已禁用，跳过处理");
                return content;
            }

            String html = safeToString(content);
            if (html.trim().isEmpty()) {
                log.warn("HTML内容为空，跳过水印处理");
                return content;
            }

            log.info("HTML内容长度: {}, 前100字符: {}",
                    html.length(),
                    html.length() > 100 ? html.substring(0, 100) : html);

            // 生成水印文本
            String watermarkText = generateWatermarkText(request, config);
            log.info("生成水印文本: {}", watermarkText);

            // 注入水印
            String watermarkedHtml = injectWatermark(html, watermarkText, request, config);

            log.info("水印注入完成，处理后长度: {}", watermarkedHtml.length());

            return safeToBytes(watermarkedHtml);

        } catch (Exception e) {
            log.error("HTML水印处理失败", e);
            return content; // 失败时返回原内容
        }
    }

    /**
     * 注入水印到HTML中
     */
    private String injectWatermark(String html, String watermarkText, 
                                 HttpServletRequest request, WebProxyConfig config) {
        
        // 1. 注入水印样式
        html = injectWatermarkStyle(html, watermarkText, config);
        
        // 2. 注入水印脚本
        html = injectWatermarkScript(html, config);
        
        // 3. 重写页面链接（如果启用）
        if (config.getEnableLinkRewrite()) {
            html = rewritePageLinks(html, request, config);
        }
        
        // 4. 注入API拦截脚本（如果启用）
        if (config.getEnableApiIntercept()) {
            html = injectApiInterceptor(html, request, config);
        }
        
        return html;
    }

    /**
     * 注入水印样式
     */
    private String injectWatermarkStyle(String html, String watermarkText, WebProxyConfig config) {
        String base64Svg = generateWatermarkSvg(watermarkText, config);
        double opacity = config.getWatermarkOpacity() != null ? config.getWatermarkOpacity() : 0.1;
        int width = config.getWatermarkWidth() != null ? config.getWatermarkWidth() : 300;
        int height = config.getWatermarkHeight() != null ? config.getWatermarkHeight() : 150;
        
        String watermarkStyle = String.format(
            "<style id=\"dwts-watermark-style\">\n" +
            ".dwts-watermark {\n" +
            "    position: fixed !important;\n" +
            "    top: 0 !important;\n" +
            "    left: 0 !important;\n" +
            "    width: 100%% !important;\n" +
            "    height: 100%% !important;\n" +
            "    pointer-events: none !important;\n" +
            "    z-index: 999999 !important;\n" +
            "    opacity: %.2f !important;\n" +
            "    background-image: url('data:image/svg+xml;base64,%s') !important;\n" +
            "    background-repeat: repeat !important;\n" +
            "    background-size: %dpx %dpx !important;\n" +
            "    user-select: none !important;\n" +
            "    -webkit-user-select: none !important;\n" +
            "    -moz-user-select: none !important;\n" +
            "    -ms-user-select: none !important;\n" +
            "}\n" +
            "</style>\n",
            opacity, base64Svg, width, height);

        log.info("生成水印样式 - 透明度: {}, 尺寸: {}x{}", opacity, width, height);
        
        // 尝试在</head>前插入样式
        if (HEAD_PATTERN.matcher(html).find()) {
            return HEAD_PATTERN.matcher(html).replaceFirst(watermarkStyle + "</head>");
        }

        // 如果没有</head>标签，尝试在<body>前插入
        if (html.toLowerCase().contains("<body")) {
            return html.replaceFirst("(?i)<body", watermarkStyle + "<body");
        }

        // 如果都没有，直接在HTML开头插入
        return watermarkStyle + html;
    }

    /**
     * 注入水印脚本
     */
    private String injectWatermarkScript(String html, WebProxyConfig config) {
        String watermarkScript =
            "<script id=\"dwts-watermark-script\">\n" +
            "(function() {\n" +
            "    'use strict';\n" +
            "    \n" +
            "    var watermarkId = 'dwts-watermark-div';\n" +
            "    var watermarkClass = 'dwts-watermark';\n" +
            "    var checkInterval = 1000; // 检查间隔1秒\n" +
            "    \n" +
            "    function createWatermark() {\n" +
            "        // 移除已存在的水印\n" +
            "        var existingWatermark = document.getElementById(watermarkId);\n" +
            "        if (existingWatermark) {\n" +
            "            existingWatermark.remove();\n" +
            "        }\n" +
            "        \n" +
            "        var watermark = document.createElement('div');\n" +
            "        watermark.id = watermarkId;\n" +
            "        watermark.className = watermarkClass;\n" +
            "        \n" +
            "        // 防止被删除和修改\n" +
            "        Object.defineProperty(watermark, 'remove', {\n" +
            "            value: function() { \n" +
            "                setTimeout(createWatermark, 100); \n" +
            "            },\n" +
            "            writable: false,\n" +
            "            configurable: false\n" +
            "        });\n" +
            "        \n" +
            "        Object.defineProperty(watermark, 'style', {\n" +
            "            value: watermark.style,\n" +
            "            writable: false,\n" +
            "            configurable: false\n" +
            "        });\n" +
            "        \n" +
            "        // 添加到页面\n" +
            "        if (document.body) {\n" +
            "            document.body.appendChild(watermark);\n" +
            "        } else {\n" +
            "            document.documentElement.appendChild(watermark);\n" +
            "        }\n" +
            "        \n" +
            "        return watermark;\n" +
            "    }\n" +
            "    \n" +
            "    function checkWatermark() {\n" +
            "        var watermark = document.getElementById(watermarkId);\n" +
            "        if (!watermark || !document.contains(watermark)) {\n" +
            "            createWatermark();\n" +
            "        }\n" +
            "    }\n" +
            "    \n" +
            "    // 页面加载完成后创建水印\n" +
            "    function initWatermark() {\n" +
            "        createWatermark();\n" +
            "        \n" +
            "        // 定期检查水印是否存在\n" +
            "        setInterval(checkWatermark, checkInterval);\n" +
            "        \n" +
            "        // 监控DOM变化\n" +
            "        if (window.MutationObserver) {\n" +
            "            var observer = new MutationObserver(function(mutations) {\n" +
            "                var needCheck = false;\n" +
            "                mutations.forEach(function(mutation) {\n" +
            "                    if (mutation.type === 'childList') {\n" +
            "                        mutation.removedNodes.forEach(function(node) {\n" +
            "                            if (node.id === watermarkId) {\n" +
            "                                needCheck = true;\n" +
            "                            }\n" +
            "                        });\n" +
            "                    }\n" +
            "                });\n" +
            "                if (needCheck) {\n" +
            "                    setTimeout(checkWatermark, 50);\n" +
            "                }\n" +
            "            });\n" +
            "            \n" +
            "            observer.observe(document.body || document.documentElement, {\n" +
            "                childList: true,\n" +
            "                subtree: true\n" +
            "            });\n" +
            "        }\n" +
            "    }\n" +
            "    \n" +
            "    // 等待DOM加载完成\n" +
            "    if (document.readyState === 'loading') {\n" +
            "        document.addEventListener('DOMContentLoaded', initWatermark);\n" +
            "    } else {\n" +
            "        initWatermark();\n" +
            "    }\n" +
            "    \n" +
            "    // 防止控制台删除水印\n" +
            "    if (window.console) {\n" +
            "        var originalLog = console.log;\n" +
            "        console.log = function() {\n" +
            "            checkWatermark();\n" +
            "            return originalLog.apply(console, arguments);\n" +
            "        };\n" +
            "    }\n" +
            "    \n" +
            "})();\n" +
            "</script>";
        
        return BODY_PATTERN.matcher(html).replaceFirst(watermarkScript + "</body>");
    }

    /**
     * 重写页面中的链接
     */
    private String rewritePageLinks(String html, HttpServletRequest request, WebProxyConfig config) {
        String proxyBaseUrl = getProxyBaseUrl(request);
        String targetBaseUrl = config.getTargetBaseUrl();
        
        // 重写绝对URL
        html = html.replaceAll("(?i)href=[\"']" + Pattern.quote(targetBaseUrl), 
                              "href=\"" + proxyBaseUrl);
        html = html.replaceAll("(?i)src=[\"']" + Pattern.quote(targetBaseUrl), 
                              "src=\"" + proxyBaseUrl);
        
        return html;
    }

    /**
     * 注入API拦截脚本
     */
    private String injectApiInterceptor(String html, HttpServletRequest request, WebProxyConfig config) {
        String proxyBaseUrl = getProxyBaseUrl(request);
        String[] apiPatterns = config.getApiPathPatterns().split(",");
        
        StringBuilder patternsJs = new StringBuilder("[");
        for (int i = 0; i < apiPatterns.length; i++) {
            if (i > 0) patternsJs.append(",");
            patternsJs.append("'").append(apiPatterns[i].trim().replace("**", "")).append("'");
        }
        patternsJs.append("]");
        
        String apiInterceptorScript = String.format(
            "<script id=\"dwts-api-interceptor\">\n" +
            "(function() {\n" +
            "    'use strict';\n" +
            "    \n" +
            "    var proxyBaseUrl = '%s';\n" +
            "    var apiPatterns = %s;\n" +
            "    \n" +
            "    function isApiUrl(url) {\n" +
            "        if (typeof url !== 'string') return false;\n" +
            "        return apiPatterns.some(function(pattern) {\n" +
            "            return url.startsWith(pattern);\n" +
            "        });\n" +
            "    }\n" +
            "    \n" +
            "    function rewriteUrl(url) {\n" +
            "        if (isApiUrl(url)) {\n" +
            "            return proxyBaseUrl + url;\n" +
            "        }\n" +
            "        return url;\n" +
            "    }\n" +
            "    \n" +
            "    // 拦截fetch请求\n" +
            "    if (window.fetch) {\n" +
            "        var originalFetch = window.fetch;\n" +
            "        window.fetch = function(url, options) {\n" +
            "            if (typeof url === 'string') {\n" +
            "                url = rewriteUrl(url);\n" +
            "            } else if (url && url.url) {\n" +
            "                url.url = rewriteUrl(url.url);\n" +
            "            }\n" +
            "            return originalFetch.call(this, url, options);\n" +
            "        };\n" +
            "    }\n" +
            "    \n" +
            "    // 拦截XMLHttpRequest\n" +
            "    if (window.XMLHttpRequest) {\n" +
            "        var originalOpen = XMLHttpRequest.prototype.open;\n" +
            "        XMLHttpRequest.prototype.open = function(method, url, async, user, password) {\n" +
            "            url = rewriteUrl(url);\n" +
            "            return originalOpen.call(this, method, url, async, user, password);\n" +
            "        };\n" +
            "    }\n" +
            "    \n" +
            "    // 拦截axios（如果存在）\n" +
            "    if (window.axios && axios.interceptors) {\n" +
            "        axios.interceptors.request.use(function(config) {\n" +
            "            if (config.url) {\n" +
            "                config.url = rewriteUrl(config.url);\n" +
            "            }\n" +
            "            return config;\n" +
            "        });\n" +
            "    }\n" +
            "    \n" +
            "})();\n" +
            "</script>",
            proxyBaseUrl, patternsJs.toString());
        
        return HEAD_PATTERN.matcher(html).replaceFirst(apiInterceptorScript + "</head>");
    }

    /**
     * 获取代理基础URL
     */
    private String getProxyBaseUrl(HttpServletRequest request) {
        StringBuilder url = new StringBuilder();
        url.append(request.getScheme()).append("://").append(request.getServerName());
        
        int port = request.getServerPort();
        if (port != 80 && port != 443) {
            url.append(":").append(port);
        }
        
        return url.toString();
    }

    @Override
    public boolean canHandle(String contentType) {
        return isContentTypeMatch(contentType, "text/html");
    }

    @Override
    public String getProcessorName() {
        return "HtmlWatermarkProcessor";
    }

    @Override
    public String getWatermarkType() {
        return "PAGE";
    }
}
