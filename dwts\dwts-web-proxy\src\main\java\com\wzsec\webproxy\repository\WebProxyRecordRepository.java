package com.wzsec.webproxy.repository;

import com.wzsec.webproxy.domain.WebProxyRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * Web代理记录Repository
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Repository
public interface WebProxyRecordRepository extends JpaRepository<WebProxyRecord, Long>, 
                                                 JpaSpecificationExecutor<WebProxyRecord> {

    /**
     * 根据代理配置ID查找记录
     *
     * @param proxyConfigId 代理配置ID
     * @return 记录列表
     */
    List<WebProxyRecord> findByProxyConfigIdOrderByCreateTimeDesc(Long proxyConfigId);

    /**
     * 根据请求IP查找记录
     *
     * @param requestIp 请求IP
     * @return 记录列表
     */
    List<WebProxyRecord> findByRequestIpOrderByCreateTimeDesc(String requestIp);

    /**
     * 根据时间范围查找记录
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 记录列表
     */
    List<WebProxyRecord> findByCreateTimeBetweenOrderByCreateTimeDesc(Timestamp startTime, 
                                                                     Timestamp endTime);

    /**
     * 查找添加了水印的记录
     *
     * @return 记录列表
     */
    List<WebProxyRecord> findByWatermarkAddedTrueOrderByCreateTimeDesc();

    /**
     * 根据请求类型统计数量
     *
     * @param requestType 请求类型
     * @return 数量
     */
    long countByRequestType(String requestType);

    /**
     * 根据水印类型统计数量
     *
     * @param watermarkType 水印类型
     * @return 数量
     */
    long countByWatermarkType(String watermarkType);

    /**
     * 统计指定时间范围内的访问量
     *
     * @param proxyConfigId 代理配置ID
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @return 访问量
     */
    @Query("SELECT COUNT(r) FROM WebProxyRecord r WHERE r.proxyConfigId = :proxyConfigId " +
           "AND r.createTime BETWEEN :startTime AND :endTime")
    long countByProxyConfigIdAndTimeRange(@Param("proxyConfigId") Long proxyConfigId,
                                         @Param("startTime") Timestamp startTime,
                                         @Param("endTime") Timestamp endTime);

    /**
     * 统计指定IP的访问量
     *
     * @param requestIp 请求IP
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 访问量
     */
    @Query("SELECT COUNT(r) FROM WebProxyRecord r WHERE r.requestIp = :requestIp " +
           "AND r.createTime BETWEEN :startTime AND :endTime")
    long countByRequestIpAndTimeRange(@Param("requestIp") String requestIp,
                                     @Param("startTime") Timestamp startTime,
                                     @Param("endTime") Timestamp endTime);

    /**
     * 查找平均处理时间
     *
     * @param proxyConfigId 代理配置ID
     * @return 平均处理时间
     */
    @Query("SELECT AVG(r.processTime) FROM WebProxyRecord r WHERE r.proxyConfigId = :proxyConfigId " +
           "AND r.processTime IS NOT NULL")
    Double findAverageProcessTimeByProxyConfigId(@Param("proxyConfigId") Long proxyConfigId);

    /**
     * 查找错误记录
     *
     * @param proxyConfigId 代理配置ID
     * @return 错误记录列表
     */
    @Query("SELECT r FROM WebProxyRecord r WHERE r.proxyConfigId = :proxyConfigId " +
           "AND r.errorMessage IS NOT NULL ORDER BY r.createTime DESC")
    List<WebProxyRecord> findErrorRecordsByProxyConfigId(@Param("proxyConfigId") Long proxyConfigId);

    /**
     * 删除指定时间之前的记录
     *
     * @param beforeTime 时间点
     * @return 删除的记录数
     */
    long deleteByCreateTimeBefore(Timestamp beforeTime);
}
