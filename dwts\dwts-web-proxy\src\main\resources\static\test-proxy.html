<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .test-section h2 {
            color: #555;
            margin-top: 0;
        }
        .proxy-button {
            display: inline-block;
            padding: 15px 30px;
            margin: 10px;
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
        }
        .proxy-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,123,255,0.4);
        }
        .status-box {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .warning-box {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .success-box {
            background: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
        }
        .test-result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 DWTS 代理测试页面</h1>
        
        <div class="status-box">
            <strong>当前页面：</strong>管理端口 9090 | <strong>测试目标：</strong>验证多端口代理和水印功能
        </div>

        <div class="test-section">
            <h2>🎯 代理端口测试</h2>
            <p>点击下面的按钮测试不同端口的代理功能：</p>
            
            <a href="http://localhost:8080/" class="proxy-button" target="_blank">
                📱 测试百度代理 (8080端口)
            </a>
            
            <a href="http://localhost:8081/" class="proxy-button" target="_blank">
                🔧 测试本地服务 (8081端口)
            </a>
            
            <a href="http://localhost:8082/" class="proxy-button" target="_blank">
                🌐 测试内网服务 (8082端口)
            </a>
            
            <div class="warning-box">
                <strong>注意：</strong>如果代理无法访问，请检查：
                <ul>
                    <li>应用是否正常启动并监听相应端口</li>
                    <li>目标服务是否可以正常访问</li>
                    <li>防火墙是否阻止了端口访问</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>💧 水印功能测试</h2>
            <p>代理页面应该包含以下水印功能：</p>
            
            <div class="success-box">
                <strong>✅ 页面水印：</strong>
                <ul>
                    <li>半透明文字水印覆盖整个页面</li>
                    <li>包含IP地址、时间戳等信息</li>
                    <li>防删除和防修改保护</li>
                </ul>
            </div>
            
            <div class="success-box">
                <strong>✅ API水印：</strong>
                <ul>
                    <li>JSON响应中添加水印字段</li>
                    <li>XML响应中添加水印节点</li>
                    <li>HTTP头中添加水印信息</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>🔍 实时状态检查</h2>
            <button onclick="checkProxyStatus()" class="proxy-button">检查代理状态</button>
            <div id="statusResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>📊 配置信息</h2>
            <div class="code-block">
                <div>管理端口: 9090 (当前页面)</div>
                <div>百度代理: 8080 -> https://www.baidu.com:443</div>
                <div>本地服务: 8081 -> http://127.0.0.1:8013</div>
                <div>内网服务: 8082 -> http://192.168.1.102:3000</div>
            </div>
        </div>

        <div class="test-section">
            <h2>🛠️ 故障排除</h2>
            <div class="warning-box">
                <strong>常见问题：</strong>
                <ol>
                    <li><strong>端口无法访问：</strong>检查应用是否正常启动，查看启动日志</li>
                    <li><strong>代理失败：</strong>检查目标服务是否可达，网络连接是否正常</li>
                    <li><strong>水印不显示：</strong>检查浏览器控制台是否有JavaScript错误</li>
                    <li><strong>HTTPS问题：</strong>某些HTTPS网站可能需要特殊配置</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        async function checkProxyStatus() {
            const resultDiv = document.getElementById('statusResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result status-box';
            resultDiv.innerHTML = '<div class="loading"></div> 正在检查代理状态...';
            
            try {
                const response = await fetch('/test/proxy-status');
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'test-result success-box';
                    resultDiv.innerHTML = `
                        <h3>✅ 代理状态检查成功</h3>
                        <p><strong>统计信息：</strong></p>
                        <ul>
                            <li>总配置数：${data.stats.totalCount}</li>
                            <li>激活配置：${data.stats.activeCount}</li>
                            <li>缓存配置：${data.stats.cachedCount}</li>
                        </ul>
                        <p><strong>配置列表：</strong></p>
                        <div class="code-block">
                            ${data.configs.map(config => 
                                `${config.proxyName}: ${config.proxyPort} -> ${config.targetHost}:${config.targetPort}`
                            ).join('<br>')}
                        </div>
                    `;
                } else {
                    throw new Error(data.error || '未知错误');
                }
            } catch (error) {
                resultDiv.className = 'test-result warning-box';
                resultDiv.innerHTML = `
                    <h3>❌ 代理状态检查失败</h3>
                    <p><strong>错误信息：</strong>${error.message}</p>
                    <p>请检查应用是否正常运行。</p>
                `;
            }
        }

        // 页面加载时自动检查状态
        window.addEventListener('load', function() {
            setTimeout(checkProxyStatus, 1000);
        });
    </script>
</body>
</html>
