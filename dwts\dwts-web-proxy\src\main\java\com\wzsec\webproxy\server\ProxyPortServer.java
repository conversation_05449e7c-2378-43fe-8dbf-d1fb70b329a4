package com.wzsec.webproxy.server;

import com.wzsec.webproxy.domain.WebProxyConfig;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServer;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.support.AnnotationConfigWebApplicationContext;
import org.springframework.web.servlet.DispatcherServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 单个端口的代理服务器
 * 为特定的代理配置创建独立的HTTP服务器
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
public class ProxyPortServer {

    private final WebProxyConfig config;
    private final ApplicationContext parentContext;
    private WebServer webServer;
    private volatile boolean running = false;

    public ProxyPortServer(WebProxyConfig config, ApplicationContext parentContext) {
        this.config = config;
        this.parentContext = parentContext;
    }

    /**
     * 启动代理服务器
     */
    public void start() throws Exception {
        if (running) {
            log.warn("代理服务器已经在运行: 端口{}", config.getProxyPort());
            return;
        }

        try {
            // 创建Tomcat服务器工厂
            TomcatServletWebServerFactory factory = new TomcatServletWebServerFactory();
            factory.setPort(config.getProxyPort());
            factory.setContextPath("");

            // 创建Web应用上下文
            AnnotationConfigWebApplicationContext webContext = new AnnotationConfigWebApplicationContext();
            webContext.setParent(parentContext);
            webContext.register(ProxyPortConfiguration.class);

            // 创建代理Servlet
            ProxyServlet proxyServlet = new ProxyServlet(config, parentContext);
            
            // 注册Servlet
            ServletRegistrationBean<ProxyServlet> servletBean = new ServletRegistrationBean<>();
            servletBean.setServlet(proxyServlet);
            servletBean.addUrlMappings("/*");
            servletBean.setName("proxyServlet-" + config.getProxyPort());

            // 创建并启动Web服务器
            webServer = factory.getWebServer(servletContext -> {
                servletContext.addServlet("proxyServlet", proxyServlet)
                        .addMapping("/*");
            });

            webServer.start();
            running = true;

            log.info("代理服务器启动成功: {} (端口:{})", config.getProxyName(), config.getProxyPort());

        } catch (Exception e) {
            running = false;
            log.error("启动代理服务器失败: {} (端口:{})", config.getProxyName(), config.getProxyPort(), e);
            throw e;
        }
    }

    /**
     * 停止代理服务器
     */
    public void stop() throws Exception {
        if (!running || webServer == null) {
            return;
        }

        try {
            webServer.stop();
            running = false;
            log.info("代理服务器已停止: {} (端口:{})", config.getProxyName(), config.getProxyPort());
        } catch (Exception e) {
            log.error("停止代理服务器失败: {} (端口:{})", config.getProxyName(), config.getProxyPort(), e);
            throw e;
        }
    }

    /**
     * 检查服务器是否正在运行
     */
    public boolean isRunning() {
        return running && webServer != null;
    }

    /**
     * 获取服务器端口
     */
    public int getPort() {
        return webServer != null ? webServer.getPort() : config.getProxyPort();
    }

    /**
     * 代理Servlet实现
     */
    private static class ProxyServlet extends HttpServlet {
        
        private final WebProxyConfig config;
        private final ApplicationContext applicationContext;

        public ProxyServlet(WebProxyConfig config, ApplicationContext applicationContext) {
            this.config = config;
            this.applicationContext = applicationContext;
        }

        @Override
        protected void service(HttpServletRequest request, HttpServletResponse response) 
                throws ServletException, IOException {
            
            try {
                // 设置代理配置到请求属性中，供WebProxyService使用
                request.setAttribute("proxyConfig", config);
                
                // 获取WebProxyService并处理请求
                var webProxyService = applicationContext.getBean("webProxyService");
                
                // 这里需要调用WebProxyService的handleProxyRequest方法
                // 但由于方法签名问题，我们需要另一种方式
                
                // 临时实现：直接转发到主应用
                String targetUrl = config.getTargetBaseUrl() + request.getRequestURI();
                if (request.getQueryString() != null) {
                    targetUrl += "?" + request.getQueryString();
                }
                
                log.info("代理请求: {} -> {}", request.getRequestURL(), targetUrl);
                
                // 这里应该实现完整的代理逻辑
                response.sendRedirect(targetUrl);
                
            } catch (Exception e) {
                log.error("处理代理请求失败", e);
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, 
                        "代理服务器内部错误: " + e.getMessage());
            }
        }
    }

    /**
     * 代理端口配置类
     */
    public static class ProxyPortConfiguration {
        // 这里可以添加特定于端口的配置
    }
}
