package com.wzsec.webproxy;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * DWTS Web代理应用启动类
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@EnableCaching
@EnableTransactionManagement
@SpringBootApplication
public class WebProxyApplication {

    public static void main(String[] args) {
        try {
            ConfigurableApplicationContext context = SpringApplication.run(WebProxyApplication.class, args);
            Environment env = context.getEnvironment();
            
            String protocol = "http";
            if (env.getProperty("server.ssl.key-store") != null) {
                protocol = "https";
            }
            
            String serverPort = env.getProperty("server.port", "8080");
            String contextPath = env.getProperty("server.servlet.context-path", "");
            String hostAddress = "localhost";
            
            try {
                hostAddress = InetAddress.getLocalHost().getHostAddress();
            } catch (UnknownHostException e) {
                log.warn("无法获取本机IP地址，使用localhost");
            }
            
            log.info("\n----------------------------------------------------------\n" +
                    "  DWTS Web代理服务启动成功！\n" +
                    "  应用名称: {}\n" +
                    "  访问地址: \n" +
                    "    本地访问: {}://localhost:{}{}\n" +
                    "    外部访问: {}://{}:{}{}\n" +
                    "  配置文件: {}\n" +
                    "  数据库: {}\n" +
                    "----------------------------------------------------------",
                    env.getProperty("spring.application.name", "dwts-web-proxy"),
                    protocol, serverPort, contextPath,
                    protocol, hostAddress, serverPort, contextPath,
                    env.getActiveProfiles().length == 0 ? "default" : String.join(",", env.getActiveProfiles()),
                    env.getProperty("spring.datasource.url", "未配置"));
            
        } catch (Exception e) {
            log.error("应用启动失败", e);
            System.exit(1);
        }
    }
}
