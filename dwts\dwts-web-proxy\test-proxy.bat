@echo off
chcp 65001 >nul
echo ========================================
echo DWTS Web代理测试脚本
echo ========================================
echo.

echo 1. 检查应用状态...
curl -s http://localhost:9090/test/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 应用运行正常
) else (
    echo ✗ 应用未启动，请先启动应用
    pause
    exit /b 1
)

echo.
echo 2. 创建测试代理配置...
curl -X POST "http://localhost:9090/api/proxy/config/create-test?port=8081" -H "Content-Type: application/json" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 测试代理配置创建成功
) else (
    echo ⚠ 测试代理配置可能已存在
)

echo.
echo 3. 创建百度代理配置...
curl -X POST "http://localhost:9090/api/proxy/config/create-baidu?port=8080" -H "Content-Type: application/json" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 百度代理配置创建成功
) else (
    echo ⚠ 百度代理配置可能已存在
)

echo.
echo 4. 检查代理状态...
curl -s http://localhost:9090/test/proxy-status
echo.

echo.
echo 5. 测试链接：
echo ----------------------------------------
echo 管理页面:     http://localhost:9090/
echo 测试页面:     http://localhost:9090/test-proxy.html
echo 本地代理:     http://localhost:8081/mock/page
echo 百度代理:     http://localhost:8080/
echo JSON API:     http://localhost:8081/mock/api/json
echo XML API:      http://localhost:8081/mock/api/xml
echo ----------------------------------------

echo.
echo 6. 自动打开测试页面...
timeout /t 2 >nul
start http://localhost:9090/
start http://localhost:8081/mock/page

echo.
echo 测试完成！请检查浏览器中的页面是否显示水印。
echo 按任意键退出...
pause >nul
