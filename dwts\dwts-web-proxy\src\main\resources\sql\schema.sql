-- DWTS Web代理数据库表结构

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `dwts-clean` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `dwts-clean`;

-- Web代理配置表
CREATE TABLE IF NOT EXISTS `dwts_web_proxy_config` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `proxy_name` VARCHAR(100) NOT NULL COMMENT '代理名称',
    `proxy_port` INT(11) NOT NULL COMMENT '代理端口',
    `target_host` VARCHAR(200) NOT NULL COMMENT '目标主机地址',
    `target_port` INT(11) NOT NULL COMMENT '目标端口',
    `target_protocol` VARCHAR(10) DEFAULT 'http' COMMENT '目标协议 (http/https)',
    `watermark_text` VARCHAR(500) DEFAULT NULL COMMENT '水印文本',
    `enable_page_watermark` TINYINT(1) DEFAULT 1 COMMENT '是否启用页面水印',
    `enable_api_watermark` TINYINT(1) DEFAULT 1 COMMENT '是否启用API水印',
    `api_path_patterns` VARCHAR(1000) DEFAULT '/api/**,/rest/**' COMMENT 'API路径模式 (逗号分隔)',
    `watermark_opacity` DOUBLE DEFAULT 0.1 COMMENT '水印透明度 (0.0-1.0)',
    `watermark_width` INT(11) DEFAULT 300 COMMENT '水印宽度',
    `watermark_height` INT(11) DEFAULT 150 COMMENT '水印高度',
    `watermark_color` VARCHAR(20) DEFAULT '#666666' COMMENT '水印颜色',
    `watermark_angle` DOUBLE DEFAULT -30.0 COMMENT '水印角度',
    `enable_link_rewrite` TINYINT(1) DEFAULT 1 COMMENT '是否启用链接重写',
    `enable_api_intercept` TINYINT(1) DEFAULT 1 COMMENT '是否启用API拦截',
    `status` VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态 (ACTIVE/INACTIVE)',
    `remark` VARCHAR(500) DEFAULT NULL COMMENT '备注',
    `create_user` VARCHAR(50) DEFAULT NULL COMMENT '创建用户',
    `create_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` VARCHAR(50) DEFAULT NULL COMMENT '更新用户',
    `update_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_proxy_port` (`proxy_port`),
    UNIQUE KEY `uk_proxy_name` (`proxy_name`),
    KEY `idx_status` (`status`),
    KEY `idx_target_host_port` (`target_host`, `target_port`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Web代理配置表';

-- Web代理访问记录表
CREATE TABLE IF NOT EXISTS `dwts_web_proxy_record` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `proxy_config_id` BIGINT(20) DEFAULT NULL COMMENT '代理配置ID',
    `request_ip` VARCHAR(50) DEFAULT NULL COMMENT '请求IP',
    `request_port` INT(11) DEFAULT NULL COMMENT '请求端口',
    `request_path` VARCHAR(1000) DEFAULT NULL COMMENT '请求路径',
    `request_method` VARCHAR(10) DEFAULT NULL COMMENT '请求方法',
    `request_type` VARCHAR(20) DEFAULT NULL COMMENT '请求类型 (PAGE/API/RESOURCE)',
    `response_status` INT(11) DEFAULT NULL COMMENT '响应状态码',
    `response_content_type` VARCHAR(100) DEFAULT NULL COMMENT '响应内容类型',
    `response_size` BIGINT(20) DEFAULT NULL COMMENT '响应大小（字节）',
    `watermark_added` TINYINT(1) DEFAULT 0 COMMENT '是否添加了水印',
    `watermark_type` VARCHAR(20) DEFAULT NULL COMMENT '水印类型 (PAGE/JSON/XML/HEADER)',
    `watermark_content` VARCHAR(1000) DEFAULT NULL COMMENT '水印内容',
    `process_time` BIGINT(20) DEFAULT NULL COMMENT '处理耗时（毫秒）',
    `user_agent` VARCHAR(500) DEFAULT NULL COMMENT '用户代理',
    `referer` VARCHAR(1000) DEFAULT NULL COMMENT '引用页面',
    `session_id` VARCHAR(100) DEFAULT NULL COMMENT '会话ID',
    `error_message` VARCHAR(1000) DEFAULT NULL COMMENT '错误信息',
    `create_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `remark` VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `idx_proxy_config_id` (`proxy_config_id`),
    KEY `idx_request_ip` (`request_ip`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_request_type` (`request_type`),
    KEY `idx_watermark_type` (`watermark_type`),
    CONSTRAINT `fk_proxy_record_config` FOREIGN KEY (`proxy_config_id`) REFERENCES `dwts_web_proxy_config` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Web代理访问记录表';

-- 创建索引优化查询性能（如果不存在）
CREATE INDEX `idx_record_time_range` ON `dwts_web_proxy_record` (`create_time`, `proxy_config_id`);
CREATE INDEX `idx_record_ip_time` ON `dwts_web_proxy_record` (`request_ip`, `create_time`);
CREATE INDEX `idx_record_watermark` ON `dwts_web_proxy_record` (`watermark_added`, `watermark_type`);

-- 创建视图：代理配置统计
CREATE OR REPLACE VIEW `v_proxy_config_stats` AS
SELECT 
    c.id,
    c.proxy_name,
    c.proxy_port,
    c.target_host,
    c.target_port,
    c.status,
    COUNT(r.id) as total_requests,
    COUNT(CASE WHEN r.watermark_added = 1 THEN 1 END) as watermark_requests,
    AVG(r.process_time) as avg_process_time,
    MAX(r.create_time) as last_access_time
FROM `dwts_web_proxy_config` c
LEFT JOIN `dwts_web_proxy_record` r ON c.id = r.proxy_config_id
GROUP BY c.id, c.proxy_name, c.proxy_port, c.target_host, c.target_port, c.status;

-- 创建视图：访问统计
CREATE OR REPLACE VIEW `v_access_stats` AS
SELECT 
    DATE(create_time) as access_date,
    request_type,
    watermark_type,
    COUNT(*) as request_count,
    COUNT(DISTINCT request_ip) as unique_ips,
    AVG(process_time) as avg_process_time,
    SUM(response_size) as total_response_size
FROM `dwts_web_proxy_record`
WHERE create_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY DATE(create_time), request_type, watermark_type
ORDER BY access_date DESC, request_count DESC;
