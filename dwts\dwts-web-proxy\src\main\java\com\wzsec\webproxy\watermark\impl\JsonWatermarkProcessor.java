package com.wzsec.webproxy.watermark.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.watermark.AbstractWatermarkProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * JSON接口水印处理器
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class JsonWatermarkProcessor extends AbstractWatermarkProcessor {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public byte[] processWatermark(byte[] content, String contentType, 
                                 HttpServletRequest request, WebProxyConfig config) {
        try {
            if (!config.getEnableApiWatermark()) {
                return content;
            }

            String jsonString = safeToString(content);
            if (jsonString.trim().isEmpty()) {
                return content;
            }

            // 解析JSON
            JsonNode rootNode = objectMapper.readTree(jsonString);
            
            // 生成水印信息
            String watermarkText = generateWatermarkText(request, config);
            
            // 注入水印
            JsonNode watermarkedNode = injectWatermarkToJson(rootNode, watermarkText, request, config);
            
            // 转换回JSON字符串
            String watermarkedJson = objectMapper.writeValueAsString(watermarkedNode);
            
            return safeToBytes(watermarkedJson);
            
        } catch (Exception e) {
            log.error("JSON水印处理失败", e);
            return content; // 失败时返回原内容
        }
    }

    /**
     * 向JSON中注入水印
     */
    private JsonNode injectWatermarkToJson(JsonNode rootNode, String watermarkText, 
                                         HttpServletRequest request, WebProxyConfig config) {
        
        if (rootNode.isObject()) {
            return injectWatermarkToObject((ObjectNode) rootNode, watermarkText, request, config);
        } else if (rootNode.isArray()) {
            return injectWatermarkToArray((ArrayNode) rootNode, watermarkText, request, config);
        } else {
            // 对于基本类型，包装成对象
            ObjectNode wrapper = objectMapper.createObjectNode();
            wrapper.set("data", rootNode);
            return injectWatermarkToObject(wrapper, watermarkText, request, config);
        }
    }

    /**
     * 向JSON对象注入水印
     */
    private ObjectNode injectWatermarkToObject(ObjectNode objectNode, String watermarkText, 
                                             HttpServletRequest request, WebProxyConfig config) {
        
        // 创建水印信息对象
        ObjectNode watermarkInfo = createWatermarkInfo(watermarkText, request, config);
        
        // 在根级别添加水印字段
        objectNode.set("_watermark", watermarkInfo);
        
        return objectNode;
    }

    /**
     * 向JSON数组注入水印
     */
    private ArrayNode injectWatermarkToArray(ArrayNode arrayNode, String watermarkText, 
                                           HttpServletRequest request, WebProxyConfig config) {
        
        // 创建水印信息对象
        ObjectNode watermarkInfo = createWatermarkInfo(watermarkText, request, config);
        
        // 在数组开头插入水印对象
        ObjectNode watermarkObject = objectMapper.createObjectNode();
        watermarkObject.set("_watermark", watermarkInfo);
        watermarkObject.put("_type", "watermark");
        
        arrayNode.insert(0, watermarkObject);
        
        return arrayNode;
    }

    /**
     * 创建水印信息对象
     */
    private ObjectNode createWatermarkInfo(String watermarkText, HttpServletRequest request, WebProxyConfig config) {
        ObjectNode watermarkInfo = objectMapper.createObjectNode();
        
        // 基本水印信息
        watermarkInfo.put("text", watermarkText);
        watermarkInfo.put("timestamp", System.currentTimeMillis());
        watermarkInfo.put("source", "DWTS-WEB-PROXY");
        
        // 请求信息
        watermarkInfo.put("ip", getClientIpAddress(request));
        watermarkInfo.put("user", getCurrentUser(request));
        watermarkInfo.put("path", request.getRequestURI());
        watermarkInfo.put("method", request.getMethod());
        
        // 代理信息
        if (config != null) {
            watermarkInfo.put("proxy_name", config.getProxyName());
            watermarkInfo.put("proxy_port", config.getProxyPort());
        }
        
        // 会话信息
        if (request.getSession(false) != null) {
            watermarkInfo.put("session_id", request.getSession().getId());
        }
        
        // 用户代理
        String userAgent = request.getHeader("User-Agent");
        if (userAgent != null && userAgent.length() > 100) {
            userAgent = userAgent.substring(0, 100) + "...";
        }
        watermarkInfo.put("user_agent", userAgent);
        
        return watermarkInfo;
    }

    @Override
    public boolean canHandle(String contentType) {
        return isContentTypeMatch(contentType, "application/json", "text/json");
    }

    @Override
    public String getProcessorName() {
        return "JsonWatermarkProcessor";
    }

    @Override
    public String getWatermarkType() {
        return "JSON";
    }
}
