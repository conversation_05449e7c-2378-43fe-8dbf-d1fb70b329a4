package com.wzsec.webproxy.service;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.domain.WebProxyRecord;
import com.wzsec.webproxy.repository.WebProxyRecordRepository;
import com.wzsec.webproxy.watermark.WatermarkProcessor;
import com.wzsec.webproxy.watermark.WatermarkProcessorFactory;
import com.wzsec.webproxy.watermark.impl.HeaderWatermarkProcessor;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.sql.Timestamp;
import java.util.Enumeration;

/**
 * Web代理服务
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Service
public class WebProxyService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private WebProxyConfigService configService;

    @Autowired
    private WatermarkProcessorFactory watermarkProcessorFactory;

    @Autowired
    private WebProxyRecordRepository recordRepository;

    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    /**
     * 处理代理请求
     */
    public ResponseEntity<?> handleProxyRequest(HttpServletRequest request, 
                                              HttpServletResponse response,
                                              byte[] body) throws Exception {
        
        long startTime = System.currentTimeMillis();
        WebProxyRecord record = new WebProxyRecord();
        
        try {
            // 1. 获取代理配置
            WebProxyConfig config = getProxyConfig(request);
            if (config == null || !config.isValid()) {
                log.warn("未找到有效的代理配置，请求路径: {}, Host: {}",
                        request.getRequestURI(), request.getHeader("Host"));
                return ResponseEntity.notFound().build();
            }
            
            // 2. 初始化访问记录
            initProxyRecord(record, request, config);
            
            // 3. 构建目标URL
            String targetUrl = buildTargetUrl(request, config);
            log.debug("代理目标URL: {}", targetUrl);
            
            // 4. 复制请求头
            HttpHeaders headers = copyRequestHeaders(request);
            
            // 5. 创建请求实体
            HttpEntity<byte[]> entity = new HttpEntity<>(body, headers);
            
            // 6. 转发请求到目标服务器
            ResponseEntity<byte[]> targetResponse = restTemplate.exchange(
                targetUrl,
                HttpMethod.valueOf(request.getMethod()),
                entity,
                byte[].class
            );
            
            // 7. 处理响应
            ResponseEntity<?> processedResponse = processResponse(
                targetResponse, request, config, record);
            
            // 8. 更新访问记录
            updateProxyRecord(record, targetResponse, processedResponse, startTime);
            
            return processedResponse;
            
        } catch (Exception e) {
            // 记录错误信息
            record.setErrorMessage(e.getMessage());
            record.setProcessTime(System.currentTimeMillis() - startTime);
            
            log.error("代理请求处理失败: {}", e.getMessage(), e);
            throw e;
        } finally {
            // 异步保存访问记录
            saveProxyRecordAsync(record);
        }
    }

    /**
     * 获取代理配置
     * 支持多种方式：
     * 1. 通过请求路径前缀 /proxy/{proxyName}
     * 2. 通过Host头中的端口号
     * 3. 默认使用第一个可用配置
     */
    private WebProxyConfig getProxyConfig(HttpServletRequest request) {
        String requestPath = request.getRequestURI();
        String hostHeader = request.getHeader("Host");

        // 方法1: 通过路径前缀识别代理配置
        if (requestPath.startsWith("/proxy/")) {
            String[] pathParts = requestPath.split("/");
            if (pathParts.length >= 3) {
                String proxyName = pathParts[2];
                WebProxyConfig config = configService.getConfigByName(proxyName);
                if (config != null) {
                    log.debug("通过路径前缀找到代理配置: {}", proxyName);
                    return config;
                }
            }
        }

        // 方法2: 通过Host头中的端口号识别
        if (hostHeader != null && hostHeader.contains(":")) {
            try {
                String portStr = hostHeader.split(":")[1];
                Integer port = Integer.parseInt(portStr);
                WebProxyConfig config = configService.getConfigByPort(port);
                if (config != null) {
                    log.debug("通过Host端口找到代理配置: {}", port);
                    return config;
                }
            } catch (Exception e) {
                log.debug("解析Host头端口失败: {}", hostHeader);
            }
        }

        // 方法3: 默认使用百度代理配置（如果存在）
        WebProxyConfig baiduConfig = configService.getConfigByName("百度代理");
        if (baiduConfig != null) {
            log.debug("使用默认百度代理配置");
            return baiduConfig;
        }

        // 方法4: 使用第一个可用配置
        var configs = configService.getAllActiveConfigs();
        if (!configs.isEmpty()) {
            WebProxyConfig config = configs.get(0);
            log.debug("使用第一个可用代理配置: {}", config.getProxyName());
            return config;
        }

        return null;
    }

    /**
     * 构建目标URL
     */
    private String buildTargetUrl(HttpServletRequest request, WebProxyConfig config) {
        StringBuilder targetUrl = new StringBuilder();

        // 协议和主机
        targetUrl.append(config.getTargetBaseUrl());

        // 处理路径
        String requestPath = request.getRequestURI();

        // 如果是通过 /proxy/{proxyName} 访问的，需要去掉前缀
        if (requestPath.startsWith("/proxy/")) {
            String[] pathParts = requestPath.split("/", 4); // 分割为最多4部分
            if (pathParts.length >= 4) {
                // /proxy/{proxyName}/actual/path -> /actual/path
                requestPath = "/" + pathParts[3];
            } else if (pathParts.length == 3) {
                // /proxy/{proxyName} -> /
                requestPath = "/";
            }
        }

        // 添加路径
        targetUrl.append(requestPath);

        // 查询参数
        if (request.getQueryString() != null) {
            targetUrl.append("?").append(request.getQueryString());
        }

        return targetUrl.toString();
    }

    /**
     * 复制请求头
     */
    private HttpHeaders copyRequestHeaders(HttpServletRequest request) {
        HttpHeaders headers = new HttpHeaders();
        
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            
            // 跳过一些代理相关的头部
            if (shouldSkipHeader(headerName)) {
                continue;
            }
            
            Enumeration<String> headerValues = request.getHeaders(headerName);
            while (headerValues.hasMoreElements()) {
                headers.add(headerName, headerValues.nextElement());
            }
        }
        
        return headers;
    }

    /**
     * 检查是否应该跳过某个请求头
     */
    private boolean shouldSkipHeader(String headerName) {
        String lowerName = headerName.toLowerCase();
        return lowerName.equals("host") || 
               lowerName.equals("content-length") ||
               lowerName.startsWith("x-forwarded-") ||
               lowerName.equals("connection") ||
               lowerName.equals("upgrade") ||
               lowerName.equals("proxy-connection");
    }

    /**
     * 处理响应
     */
    private ResponseEntity<?> processResponse(ResponseEntity<byte[]> targetResponse,
                                            HttpServletRequest request,
                                            WebProxyConfig config,
                                            WebProxyRecord record) throws Exception {
        
        String contentType = getResponseContentType(targetResponse);
        String requestPath = request.getRequestURI();
        byte[] originalContent = targetResponse.getBody();
        
        // 判断内容类型并选择处理策略
        if (isHtmlContent(contentType) && config.getEnablePageWatermark()) {
            // HTML页面处理
            return processHtmlContent(targetResponse, request, config, record);
            
        } else if (isApiRequest(requestPath, config) && 
                  (isJsonOrXmlContent(contentType)) && 
                  config.getEnableApiWatermark()) {
            // API接口处理
            return processApiContent(targetResponse, request, config, record);
            
        } else {
            // 其他内容处理（可能添加Header水印）
            return processOtherContent(targetResponse, request, config, record);
        }
    }

    /**
     * 处理HTML内容
     */
    private ResponseEntity<?> processHtmlContent(ResponseEntity<byte[]> response,
                                               HttpServletRequest request,
                                               WebProxyConfig config,
                                               WebProxyRecord record) throws Exception {
        
        WatermarkProcessor processor = watermarkProcessorFactory.getHtmlProcessor();
        byte[] watermarkedContent = processor.processWatermark(
            response.getBody(),
            getResponseContentType(response),
            request,
            config
        );
        
        // 更新记录
        record.setRequestType("PAGE");
        record.setWatermarkAdded(true);
        record.setWatermarkType("PAGE");
        record.setResponseSize((long) watermarkedContent.length);
        
        // 更新响应头
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.putAll(response.getHeaders());
        responseHeaders.setContentLength(watermarkedContent.length);
        
        return new ResponseEntity<>(watermarkedContent, responseHeaders, response.getStatusCode());
    }

    /**
     * 处理API内容
     */
    private ResponseEntity<?> processApiContent(ResponseEntity<byte[]> response,
                                              HttpServletRequest request,
                                              WebProxyConfig config,
                                              WebProxyRecord record) throws Exception {
        
        String contentType = getResponseContentType(response);
        WatermarkProcessor processor = watermarkProcessorFactory.getProcessor(contentType);
        
        byte[] watermarkedContent = processor.processWatermark(
            response.getBody(),
            contentType,
            request,
            config
        );
        
        // 更新记录
        record.setRequestType("API");
        record.setWatermarkAdded(true);
        record.setWatermarkType(processor.getWatermarkType());
        record.setResponseSize((long) watermarkedContent.length);
        
        // 更新响应头
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.putAll(response.getHeaders());
        responseHeaders.setContentLength(watermarkedContent.length);
        
        return new ResponseEntity<>(watermarkedContent, responseHeaders, response.getStatusCode());
    }

    /**
     * 处理其他内容
     */
    private ResponseEntity<?> processOtherContent(ResponseEntity<byte[]> response,
                                                HttpServletRequest request,
                                                WebProxyConfig config,
                                                WebProxyRecord record) throws Exception {
        
        // 更新记录
        record.setRequestType("RESOURCE");
        record.setWatermarkAdded(false);
        record.setResponseSize(response.getBody() != null ? (long) response.getBody().length : 0L);
        
        // 添加Header水印（如果启用）
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.putAll(response.getHeaders());
        
        if (config.getEnableApiWatermark()) {
            HeaderWatermarkProcessor headerProcessor = watermarkProcessorFactory.getHeaderProcessor();
            String watermarkHeader = headerProcessor.generateWatermarkHeader(request, config);
            
            responseHeaders.add("X-DWTS-Watermark", watermarkHeader);
            responseHeaders.add("X-DWTS-Timestamp", String.valueOf(System.currentTimeMillis()));
            responseHeaders.add("X-DWTS-IP", getClientIpAddress(request));
            
            record.setWatermarkAdded(true);
            record.setWatermarkType("HEADER");
        }
        
        return new ResponseEntity<>(response.getBody(), responseHeaders, response.getStatusCode());
    }

    // 其他辅助方法...
    private String getResponseContentType(ResponseEntity<byte[]> response) {
        MediaType contentType = response.getHeaders().getContentType();
        return contentType != null ? contentType.toString() : null;
    }

    private boolean isHtmlContent(String contentType) {
        return contentType != null && contentType.toLowerCase().contains("text/html");
    }

    private boolean isJsonOrXmlContent(String contentType) {
        if (contentType == null) return false;
        String lowerType = contentType.toLowerCase();
        return lowerType.contains("application/json") || 
               lowerType.contains("application/xml") || 
               lowerType.contains("text/xml");
    }

    private boolean isApiRequest(String requestPath, WebProxyConfig config) {
        String[] patterns = config.getApiPathPatterns().split(",");
        for (String pattern : patterns) {
            if (pathMatcher.match(pattern.trim(), requestPath)) {
                return true;
            }
        }
        return false;
    }

    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        String remoteAddr = request.getRemoteAddr();
        if ("127.0.0.1".equals(remoteAddr) || "0:0:0:0:0:0:0:1".equals(remoteAddr)) {
            try {
                remoteAddr = InetAddress.getLocalHost().getHostAddress();
            } catch (UnknownHostException e) {
                log.warn("获取本机IP失败", e);
            }
        }
        
        return remoteAddr;
    }

    private void initProxyRecord(WebProxyRecord record, HttpServletRequest request, WebProxyConfig config) {
        record.setProxyConfigId(config.getId());
        record.setRequestIp(getClientIpAddress(request));
        record.setRequestPort(request.getRemotePort());
        record.setRequestPath(request.getRequestURI());
        record.setRequestMethod(request.getMethod());
        record.setUserAgent(request.getHeader("User-Agent"));
        record.setReferer(request.getHeader("Referer"));
        
        if (request.getSession(false) != null) {
            record.setSessionId(request.getSession().getId());
        }
    }

    private void updateProxyRecord(WebProxyRecord record, 
                                 ResponseEntity<byte[]> targetResponse,
                                 ResponseEntity<?> processedResponse,
                                 long startTime) {
        record.setResponseStatus(targetResponse.getStatusCodeValue());
        record.setResponseContentType(getResponseContentType(targetResponse));
        record.setProcessTime(System.currentTimeMillis() - startTime);
    }

    private void saveProxyRecordAsync(WebProxyRecord record) {
        try {
            recordRepository.save(record);
        } catch (Exception e) {
            log.error("保存代理访问记录失败", e);
        }
    }
}
