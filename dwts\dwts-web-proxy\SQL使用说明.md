# DWTS Web代理服务 - 数据库初始化说明

## 📋 SQL文件说明

项目提供了3个SQL文件，请根据需要选择使用：

### 1. `init.sql` - 完整初始化脚本（推荐）
**用途**: 一键完成所有数据库初始化工作
**内容**: 
- 创建数据库
- 删除旧表（如果存在）
- 创建新表结构
- 创建索引
- 插入示例数据
- 创建存储过程和视图
- 显示初始化结果

**使用方法**:
```bash
mysql -u root -p < src/main/resources/sql/init.sql
```

### 2. `schema.sql` - 表结构脚本
**用途**: 仅创建表结构和索引
**内容**:
- 创建数据库
- 创建表结构
- 创建索引
- 创建视图

**使用方法**:
```bash
mysql -u root -p < src/main/resources/sql/schema.sql
```

### 3. `data.sql` - 数据初始化脚本
**用途**: 插入示例数据和创建存储过程
**内容**:
- 插入示例配置数据
- 创建存储过程

**使用方法**:
```bash
mysql -u root -p < src/main/resources/sql/data.sql
```

## 🚀 快速开始

### 方式一：使用完整初始化脚本（推荐）
```bash
# 1. 进入项目目录
cd dwts/dwts-web-proxy

# 2. 执行完整初始化
mysql -u root -p < src/main/resources/sql/init.sql

# 3. 输入MySQL密码，等待执行完成
```

### 方式二：分步执行
```bash
# 1. 创建表结构
mysql -u root -p < src/main/resources/sql/schema.sql

# 2. 插入初始化数据
mysql -u root -p < src/main/resources/sql/data.sql
```

## 📊 初始化后的数据

执行完成后，`dwts-clean`数据库将包含以下示例配置：

| 代理名称 | 代理端口 | 目标地址 | 状态 | 说明 |
|---------|---------|----------|------|------|
| Vue应用代理示例 | 8080 | *************:80 | ACTIVE | 完整的Vue应用代理 |
| API服务代理 | 8081 | *************:8080 | ACTIVE | 仅API接口代理 |
| 管理后台代理 | 8082 | *************:3000 | ACTIVE | 管理后台代理 |
| 测试环境代理 | 8083 | test.example.com:80 | INACTIVE | 测试配置（已禁用） |
| 配置说明 | 9999 | example.com:80 | INACTIVE | 配置说明示例 |

## 🔧 配置修改

### 修改代理配置
```sql
-- 修改目标地址
UPDATE dwts_web_proxy_config 
SET target_host = '你的目标IP', target_port = 你的目标端口
WHERE proxy_name = 'Vue应用代理示例';

-- 修改水印文本
UPDATE dwts_web_proxy_config 
SET watermark_text = '你的水印文本_{IP}_{DATE}'
WHERE proxy_name = 'Vue应用代理示例';

-- 启用/禁用代理
UPDATE dwts_web_proxy_config 
SET status = 'ACTIVE'  -- 或 'INACTIVE'
WHERE proxy_name = 'Vue应用代理示例';
```

### 添加新的代理配置
```sql
INSERT INTO dwts_web_proxy_config (
    proxy_name, proxy_port, target_host, target_port, target_protocol,
    watermark_text, enable_page_watermark, enable_api_watermark,
    api_path_patterns, status, remark, create_user
) VALUES (
    '我的应用代理',           -- 代理名称
    8090,                    -- 代理端口
    '*************',         -- 目标主机
    80,                      -- 目标端口
    'http',                  -- 协议
    '我的水印_{IP}_{DATE}',   -- 水印文本
    1,                       -- 启用页面水印
    1,                       -- 启用API水印
    '/api/**,/rest/**',      -- API路径模式
    'ACTIVE',                -- 状态
    '我的应用代理配置',       -- 备注
    'admin'                  -- 创建用户
);
```

## 📈 数据查询

### 查看代理配置统计
```sql
-- 使用视图查看配置统计
SELECT * FROM v_proxy_config_stats;

-- 查看指定代理的统计信息
CALL sp_get_proxy_stats(1, 7);  -- 配置ID=1，最近7天
```

### 查看访问记录
```sql
-- 查看最近的访问记录
SELECT * FROM dwts_web_proxy_record 
ORDER BY create_time DESC 
LIMIT 10;

-- 查看访问统计
SELECT * FROM v_access_stats;
```

### 清理过期记录
```sql
-- 清理30天前的访问记录
CALL sp_cleanup_old_records(30);
```

## ⚠️ 注意事项

1. **端口冲突**: 确保代理端口没有被其他服务占用
2. **目标地址**: 确保目标服务器地址可以访问
3. **数据库权限**: 确保MySQL用户有创建数据库和表的权限
4. **字符编码**: 使用utf8mb4编码支持中文和特殊字符
5. **索引优化**: 大量数据时建议定期清理访问记录

## 🔍 故障排除

### 常见错误及解决方案

1. **1064语法错误**
   - 确保使用的是MySQL 5.7+版本
   - 检查SQL语句是否完整

2. **1050表已存在**
   - 使用`init.sql`会自动删除旧表
   - 或手动删除：`DROP TABLE IF EXISTS table_name;`

3. **1062重复键错误**
   - 检查代理端口是否重复
   - 检查代理名称是否重复

4. **连接被拒绝**
   - 检查MySQL服务是否启动
   - 检查用户名密码是否正确
   - 检查防火墙设置

### 验证安装
```sql
-- 检查表是否创建成功
SHOW TABLES LIKE 'dwts_web_proxy%';

-- 检查数据是否插入成功
SELECT COUNT(*) FROM dwts_web_proxy_config;
SELECT COUNT(*) FROM dwts_web_proxy_record;

-- 检查存储过程是否创建成功
SHOW PROCEDURE STATUS WHERE Name LIKE 'sp_%';
```

## 📞 技术支持

如果遇到问题，请：
1. 检查MySQL版本（推荐8.0+）
2. 确认SQL文件编码为UTF-8
3. 查看MySQL错误日志
4. 联系技术支持团队
