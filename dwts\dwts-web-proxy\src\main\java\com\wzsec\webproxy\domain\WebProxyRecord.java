package com.wzsec.webproxy.domain;

import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * Web代理访问记录实体类
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Entity
@Data
@Table(name = "dwts_web_proxy_record")
public class WebProxyRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 代理配置ID
     */
    @Column(name = "proxy_config_id")
    private Long proxyConfigId;

    /**
     * 请求IP
     */
    @Column(name = "request_ip", length = 50)
    private String requestIp;

    /**
     * 请求端口
     */
    @Column(name = "request_port")
    private Integer requestPort;

    /**
     * 请求路径
     */
    @Column(name = "request_path", length = 1000)
    private String requestPath;

    /**
     * 请求方法
     */
    @Column(name = "request_method", length = 10)
    private String requestMethod;

    /**
     * 请求类型 (PAGE/API/RESOURCE)
     */
    @Column(name = "request_type", length = 20)
    private String requestType;

    /**
     * 响应状态码
     */
    @Column(name = "response_status")
    private Integer responseStatus;

    /**
     * 响应内容类型
     */
    @Column(name = "response_content_type", length = 100)
    private String responseContentType;

    /**
     * 响应大小（字节）
     */
    @Column(name = "response_size")
    private Long responseSize;

    /**
     * 是否添加了水印
     */
    @Column(name = "watermark_added")
    private Boolean watermarkAdded = false;

    /**
     * 水印类型 (PAGE/JSON/XML/HEADER)
     */
    @Column(name = "watermark_type", length = 20)
    private String watermarkType;

    /**
     * 水印内容
     */
    @Column(name = "watermark_content", length = 1000)
    private String watermarkContent;

    /**
     * 处理耗时（毫秒）
     */
    @Column(name = "process_time")
    private Long processTime;

    /**
     * 用户代理
     */
    @Column(name = "user_agent", length = 500)
    private String userAgent;

    /**
     * 引用页面
     */
    @Column(name = "referer", length = 1000)
    private String referer;

    /**
     * 会话ID
     */
    @Column(name = "session_id", length = 100)
    private String sessionId;

    /**
     * 错误信息
     */
    @Column(name = "error_message", length = 1000)
    private String errorMessage;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @CreationTimestamp
    private Timestamp createTime;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;
}
