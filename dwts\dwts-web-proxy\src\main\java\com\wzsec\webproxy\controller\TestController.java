package com.wzsec.webproxy.controller;

import com.wzsec.webproxy.service.WebProxyConfigService;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired
    private WebProxyConfigService configService;

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("timestamp", System.currentTimeMillis());
        result.put("message", "DWTS Web代理服务运行正常");
        
        return ResponseEntity.ok(result);
    }

    /**
     * 获取代理状态
     */
    @GetMapping("/proxy-status")
    public ResponseEntity<Map<String, Object>> getProxyStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> stats = configService.getConfigStats();
            result.put("success", true);
            result.put("stats", stats);
            result.put("configs", configService.getAllActiveConfigs());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 代理使用说明
     */
    @GetMapping("/usage")
    public ResponseEntity<Map<String, Object>> getUsage() {
        Map<String, Object> result = new HashMap<>();
        result.put("title", "DWTS Web代理使用说明");
        result.put("description", "通过配置的代理端口访问目标网站，自动添加水印");
        
        Map<String, String> examples = new HashMap<>();
        examples.put("方式1-直接端口代理", "http://localhost:8080/ -> https://www.baidu.com (推荐)");
        examples.put("方式2-路径代理", "http://localhost:9090/proxy/百度代理/ -> https://www.baidu.com");
        examples.put("方式3-默认代理", "http://localhost:9090/ -> 使用默认配置");

        result.put("examples", examples);

        Map<String, String> instructions = new HashMap<>();
        instructions.put("步骤1", "应用启动后会自动监听所有配置的代理端口");
        instructions.put("步骤2", "直接访问 http://localhost:8080/ 代理百度（带水印）");
        instructions.put("步骤3", "访问 http://localhost:8081/ 代理本地服务");
        instructions.put("步骤4", "访问 http://localhost:8082/ 代理内网服务");
        instructions.put("注意", "每个端口都会自动添加相应的水印");

        result.put("instructions", instructions);

        // 添加端口状态信息
        try {
            var configs = configService.getAllActiveConfigs();
            Map<String, String> portStatus = new HashMap<>();
            for (var config : configs) {
                portStatus.put("端口" + config.getProxyPort(),
                        config.getProxyName() + " -> " + config.getTargetHost() + ":" + config.getTargetPort());
            }
            result.put("portStatus", portStatus);
        } catch (Exception e) {
            log.warn("获取端口状态失败", e);
        }
        
        Map<String, String> apis = new HashMap<>();
        apis.put("配置管理", "GET /api/proxy/config/list - 获取所有配置");
        apis.put("创建百度代理", "POST /api/proxy/config/create-baidu?port=8083 - 快速创建百度代理");
        apis.put("代理状态", "GET /test/proxy-status - 查看代理状态");
        
        result.put("apis", apis);
        
        return ResponseEntity.ok(result);
    }
}
