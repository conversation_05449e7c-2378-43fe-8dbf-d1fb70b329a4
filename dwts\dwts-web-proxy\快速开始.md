# DWTS Web代理 - 5分钟快速开始

## 🎯 一句话说明
**这是一个Web代理服务，可以为任意网站添加页面水印和API接口水印，无需修改原网站代码。**

## ⚡ 3步启动

### 第1步：初始化数据库
```bash
mysql -u root -p < src/main/resources/sql/init.sql
```

### 第2步：修改数据库密码
编辑 `src/main/resources/application.yml` 第16行：
```yaml
password: 你的MySQL密码
```

### 第3步：启动服务
```bash
mvn spring-boot:run
```

## 🌐 立即体验

### 代理任意网站
假设你要代理 `http://www.baidu.com`：

1. **修改代理目标**：
```sql
UPDATE dwts_web_proxy_config 
SET target_host = 'www.baidu.com', target_port = 80
WHERE proxy_name = 'Vue应用代理示例';
```

2. **访问对比**：
```
原网站：http://www.baidu.com        （无水印）
代理后：http://localhost:8080      （有水印）
```

### 代理本地Vue应用
假设你的Vue应用运行在 `http://localhost:3000`：

1. **修改代理目标**：
```sql
UPDATE dwts_web_proxy_config 
SET target_host = '127.0.0.1', target_port = 3000
WHERE proxy_name = 'Vue应用代理示例';
```

2. **访问对比**：
```
原应用：http://localhost:3000      （无水印）
代理后：http://localhost:8080      （有水印）
```

## 🎨 水印效果预览

### 页面水印
- ✅ 半透明重复水印覆盖整个页面
- ✅ 包含访问者IP和时间信息
- ✅ 防止被删除或修改
- ✅ 不影响页面正常使用

### API水印
原始API响应：
```json
{"users": [{"id": 1, "name": "张三"}]}
```

代理后API响应：
```json
{
  "users": [{"id": 1, "name": "张三"}],
  "_watermark": {
    "text": "DWTS水印_*************_20241219",
    "ip": "*************",
    "timestamp": 1703001234567
  }
}
```

## 🔧 常用配置

### 修改水印文本
```sql
UPDATE dwts_web_proxy_config 
SET watermark_text = '公司内部_{IP}_{DATE}'
WHERE id = 1;
```

### 只要API水印，不要页面水印
```sql
UPDATE dwts_web_proxy_config 
SET enable_page_watermark = 0, enable_api_watermark = 1
WHERE id = 1;
```

### 添加新的代理端口
```sql
INSERT INTO dwts_web_proxy_config (
    proxy_name, proxy_port, target_host, target_port,
    watermark_text, status
) VALUES (
    '新代理', 8090, '*************', 80,
    '新水印_{IP}_{TIME}', 'ACTIVE'
);
```

## 📋 默认配置说明

启动后默认包含3个代理配置：

| 代理端口 | 说明 | 默认目标 |
|---------|------|----------|
| 8080 | 主代理端口 | *************:80 |
| 8081 | API专用代理 | *************:8080 |
| 8082 | 管理后台代理 | *************:3000 |

**使用时只需修改目标地址即可！**

## 🔍 验证是否成功

### 1. 检查服务启动
访问：http://localhost:8080/actuator/health
看到 `{"status":"UP"}` 表示启动成功

### 2. 检查代理功能
```bash
# 如果目标是百度
curl -I http://localhost:8080
# 应该返回百度的响应头
```

### 3. 检查水印功能
用浏览器访问 http://localhost:8080
- 页面应该显示水印
- 按F12查看Network，API响应应该包含`_watermark`字段

## ❓ 常见问题

**Q: 启动后访问8080端口没有响应？**
A: 检查目标地址是否可访问，修改数据库中的target_host和target_port

**Q: 页面显示但没有水印？**
A: 确认enable_page_watermark=1，检查浏览器控制台是否有JS错误

**Q: API没有水印？**
A: 确认enable_api_watermark=1，检查请求路径是否匹配api_path_patterns

**Q: 如何代理HTTPS网站？**
A: 修改target_protocol='https'

## 🎉 完成！

现在你已经成功启动了DWTS Web代理服务！

- 🌐 可以代理任意网站
- 🎨 自动添加页面和API水印  
- 📊 记录所有访问日志
- 🔒 实现数据溯源和安全防护

需要更多配置选项，请查看 `启动使用说明.md`
