package com.wzsec.webproxy.config;

import com.wzsec.webproxy.service.WebProxyConfigService;
import com.wzsec.webproxy.util.PortChecker;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 应用启动监听器
 * 在应用完全启动后检查端口状态
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class ApplicationStartupListener {

    @Autowired
    private WebProxyConfigService configService;

    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        log.info("=== DWTS Web代理服务启动完成 ===");
        
        try {
            // 检查代理配置
            var configs = configService.getAllActiveConfigs();
            log.info("已加载{}个代理配置:", configs.size());
            
            for (var config : configs) {
                log.info("  - {} (端口:{}) -> {}:{}", 
                        config.getProxyName(),
                        config.getProxyPort(),
                        config.getTargetHost(),
                        config.getTargetPort());
            }
            
            // 检查端口监听状态
            log.info("检查端口监听状态:");
            
            // 检查主应用端口
            boolean mainPortListening = PortChecker.isPortListening("localhost", 9090);
            log.info("  - 主应用端口 9090: {}", mainPortListening ? "✓ 正在监听" : "✗ 未监听");
            
            // 检查代理端口
            for (var config : configs) {
                int port = config.getProxyPort();
                boolean listening = PortChecker.isPortListening("localhost", port);
                log.info("  - 代理端口 {}: {} ({})", 
                        port, 
                        listening ? "✓ 正在监听" : "✗ 未监听",
                        config.getProxyName());
            }
            
            // 输出访问说明
            log.info("=== 访问方式 ===");
            log.info("管理页面: http://localhost:9090/");
            log.info("测试页面: http://localhost:9090/test-proxy.html");
            
            for (var config : configs) {
                if (config.getProxyPort() != 9090) {
                    log.info("代理访问: http://localhost:{}/ -> {}:{} ({})", 
                            config.getProxyPort(),
                            config.getTargetHost(),
                            config.getTargetPort(),
                            config.getProxyName());
                }
            }
            
            log.info("路径代理: http://localhost:9090/proxy/{代理名称}/");
            log.info("参数代理: http://localhost:9090/?proxy={代理名称}");
            
            log.info("=== 启动完成 ===");
            
        } catch (Exception e) {
            log.error("启动后检查失败", e);
        }
    }
}
